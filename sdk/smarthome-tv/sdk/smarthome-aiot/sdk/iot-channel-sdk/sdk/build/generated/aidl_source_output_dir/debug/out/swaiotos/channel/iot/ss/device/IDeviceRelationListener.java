/*
 * This file is auto-generated.  DO NOT MODIFY.
 */
package swaiotos.channel.iot.ss.device;
// Declare any non-default types here with import statements

public interface IDeviceRelationListener extends android.os.IInterface
{
/** Local-side IPC implementation stub class. */
public static abstract class Stub extends android.os.Binder implements swaiotos.channel.iot.ss.device.IDeviceRelationListener
{
private static final java.lang.String DESCRIPTOR = "swaiotos.channel.iot.ss.device.IDeviceRelationListener";
/** Construct the stub at attach it to the interface. */
public Stub()
{
this.attachInterface(this, DESCRIPTOR);
}
/**
 * Cast an IBinder object into an swaiotos.channel.iot.ss.device.IDeviceRelationListener interface,
 * generating a proxy if needed.
 */
public static swaiotos.channel.iot.ss.device.IDeviceRelationListener asInterface(android.os.IBinder obj)
{
if ((obj==null)) {
return null;
}
android.os.IInterface iin = obj.queryLocalInterface(DESCRIPTOR);
if (((iin!=null)&&(iin instanceof swaiotos.channel.iot.ss.device.IDeviceRelationListener))) {
return ((swaiotos.channel.iot.ss.device.IDeviceRelationListener)iin);
}
return new swaiotos.channel.iot.ss.device.IDeviceRelationListener.Stub.Proxy(obj);
}
@Override public android.os.IBinder asBinder()
{
return this;
}
@Override public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws android.os.RemoteException
{
java.lang.String descriptor = DESCRIPTOR;
switch (code)
{
case INTERFACE_TRANSACTION:
{
reply.writeString(descriptor);
return true;
}
case TRANSACTION_onDeviceBind:
{
data.enforceInterface(descriptor);
java.lang.String _arg0;
_arg0 = data.readString();
this.onDeviceBind(_arg0);
reply.writeNoException();
return true;
}
case TRANSACTION_onDeviceUnbind:
{
data.enforceInterface(descriptor);
java.lang.String _arg0;
_arg0 = data.readString();
this.onDeviceUnbind(_arg0);
reply.writeNoException();
return true;
}
default:
{
return super.onTransact(code, data, reply, flags);
}
}
}
private static class Proxy implements swaiotos.channel.iot.ss.device.IDeviceRelationListener
{
private android.os.IBinder mRemote;
Proxy(android.os.IBinder remote)
{
mRemote = remote;
}
@Override public android.os.IBinder asBinder()
{
return mRemote;
}
public java.lang.String getInterfaceDescriptor()
{
return DESCRIPTOR;
}
@Override public void onDeviceBind(java.lang.String lsid) throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
try {
_data.writeInterfaceToken(DESCRIPTOR);
_data.writeString(lsid);
mRemote.transact(Stub.TRANSACTION_onDeviceBind, _data, _reply, 0);
_reply.readException();
}
finally {
_reply.recycle();
_data.recycle();
}
}
@Override public void onDeviceUnbind(java.lang.String lsid) throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
try {
_data.writeInterfaceToken(DESCRIPTOR);
_data.writeString(lsid);
mRemote.transact(Stub.TRANSACTION_onDeviceUnbind, _data, _reply, 0);
_reply.readException();
}
finally {
_reply.recycle();
_data.recycle();
}
}
}
static final int TRANSACTION_onDeviceBind = (android.os.IBinder.FIRST_CALL_TRANSACTION + 0);
static final int TRANSACTION_onDeviceUnbind = (android.os.IBinder.FIRST_CALL_TRANSACTION + 1);
}
public void onDeviceBind(java.lang.String lsid) throws android.os.RemoteException;
public void onDeviceUnbind(java.lang.String lsid) throws android.os.RemoteException;
}
