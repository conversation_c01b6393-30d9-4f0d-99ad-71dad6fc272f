/*
 * This file is auto-generated.  DO NOT MODIFY.
 */
package com.swaiot.aiotlib.push;
/**
 * Example of defining an interface for calling on to a remote service
 * (running in another process).
 */
public interface IAIOTPushService extends android.os.IInterface
{
  /** Default implementation for IAIOTPushService. */
  public static class Default implements com.swaiot.aiotlib.push.IAIOTPushService
  {
    /**
         * Often you want to allow a service to call back to its clients.
         * This shows how to do so, by registering a callback interface with
         * the service.
         */
    @Override public void registerCallback(java.lang.String key, com.swaiot.aiotlib.push.IAIOTPushServiceCallback cb) throws android.os.RemoteException
    {
    }
    /**
         * Remove a previously registered callback interface.
         */
    @Override public void unregisterCallback(java.lang.String key, com.swaiot.aiotlib.push.IAIOTPushServiceCallback cb) throws android.os.RemoteException
    {
    }
    @Override
    public android.os.IBinder asBinder() {
      return null;
    }
  }
  /** Local-side IPC implementation stub class. */
  public static abstract class Stub extends android.os.Binder implements com.swaiot.aiotlib.push.IAIOTPushService
  {
    private static final java.lang.String DESCRIPTOR = "com.swaiot.aiotlib.push.IAIOTPushService";
    /** Construct the stub at attach it to the interface. */
    public Stub()
    {
      this.attachInterface(this, DESCRIPTOR);
    }
    /**
     * Cast an IBinder object into an com.swaiot.aiotlib.push.IAIOTPushService interface,
     * generating a proxy if needed.
     */
    public static com.swaiot.aiotlib.push.IAIOTPushService asInterface(android.os.IBinder obj)
    {
      if ((obj==null)) {
        return null;
      }
      android.os.IInterface iin = obj.queryLocalInterface(DESCRIPTOR);
      if (((iin!=null)&&(iin instanceof com.swaiot.aiotlib.push.IAIOTPushService))) {
        return ((com.swaiot.aiotlib.push.IAIOTPushService)iin);
      }
      return new com.swaiot.aiotlib.push.IAIOTPushService.Stub.Proxy(obj);
    }
    @Override public android.os.IBinder asBinder()
    {
      return this;
    }
    @Override public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws android.os.RemoteException
    {
      java.lang.String descriptor = DESCRIPTOR;
      switch (code)
      {
        case INTERFACE_TRANSACTION:
        {
          reply.writeString(descriptor);
          return true;
        }
        case TRANSACTION_registerCallback:
        {
          data.enforceInterface(descriptor);
          java.lang.String _arg0;
          _arg0 = data.readString();
          com.swaiot.aiotlib.push.IAIOTPushServiceCallback _arg1;
          _arg1 = com.swaiot.aiotlib.push.IAIOTPushServiceCallback.Stub.asInterface(data.readStrongBinder());
          this.registerCallback(_arg0, _arg1);
          reply.writeNoException();
          return true;
        }
        case TRANSACTION_unregisterCallback:
        {
          data.enforceInterface(descriptor);
          java.lang.String _arg0;
          _arg0 = data.readString();
          com.swaiot.aiotlib.push.IAIOTPushServiceCallback _arg1;
          _arg1 = com.swaiot.aiotlib.push.IAIOTPushServiceCallback.Stub.asInterface(data.readStrongBinder());
          this.unregisterCallback(_arg0, _arg1);
          reply.writeNoException();
          return true;
        }
        default:
        {
          return super.onTransact(code, data, reply, flags);
        }
      }
    }
    private static class Proxy implements com.swaiot.aiotlib.push.IAIOTPushService
    {
      private android.os.IBinder mRemote;
      Proxy(android.os.IBinder remote)
      {
        mRemote = remote;
      }
      @Override public android.os.IBinder asBinder()
      {
        return mRemote;
      }
      public java.lang.String getInterfaceDescriptor()
      {
        return DESCRIPTOR;
      }
      /**
           * Often you want to allow a service to call back to its clients.
           * This shows how to do so, by registering a callback interface with
           * the service.
           */
      @Override public void registerCallback(java.lang.String key, com.swaiot.aiotlib.push.IAIOTPushServiceCallback cb) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(key);
          _data.writeStrongBinder((((cb!=null))?(cb.asBinder()):(null)));
          boolean _status = mRemote.transact(Stub.TRANSACTION_registerCallback, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().registerCallback(key, cb);
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      /**
           * Remove a previously registered callback interface.
           */
      @Override public void unregisterCallback(java.lang.String key, com.swaiot.aiotlib.push.IAIOTPushServiceCallback cb) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(key);
          _data.writeStrongBinder((((cb!=null))?(cb.asBinder()):(null)));
          boolean _status = mRemote.transact(Stub.TRANSACTION_unregisterCallback, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().unregisterCallback(key, cb);
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      public static com.swaiot.aiotlib.push.IAIOTPushService sDefaultImpl;
    }
    static final int TRANSACTION_registerCallback = (android.os.IBinder.FIRST_CALL_TRANSACTION + 0);
    static final int TRANSACTION_unregisterCallback = (android.os.IBinder.FIRST_CALL_TRANSACTION + 1);
    public static boolean setDefaultImpl(com.swaiot.aiotlib.push.IAIOTPushService impl) {
      if (Stub.Proxy.sDefaultImpl == null && impl != null) {
        Stub.Proxy.sDefaultImpl = impl;
        return true;
      }
      return false;
    }
    public static com.swaiot.aiotlib.push.IAIOTPushService getDefaultImpl() {
      return Stub.Proxy.sDefaultImpl;
    }
  }
  /**
       * Often you want to allow a service to call back to its clients.
       * This shows how to do so, by registering a callback interface with
       * the service.
       */
  public void registerCallback(java.lang.String key, com.swaiot.aiotlib.push.IAIOTPushServiceCallback cb) throws android.os.RemoteException;
  /**
       * Remove a previously registered callback interface.
       */
  public void unregisterCallback(java.lang.String key, com.swaiot.aiotlib.push.IAIOTPushServiceCallback cb) throws android.os.RemoteException;
}
