/*
 * This file is auto-generated.  DO NOT MODIFY.
 */
package com.swaiot.aiotlib.family;
// Declare any non-default types here with import statements

public interface IFamilyManager extends android.os.IInterface
{
  /** Default implementation for IFamilyManager. */
  public static class Default implements com.swaiot.aiotlib.family.IFamilyManager
  {
    @Override public void setCurrentFamily(java.lang.String familyId, java.lang.String commandId) throws android.os.RemoteException
    {
    }
    @Override public void addFamily(java.lang.String commandId) throws android.os.RemoteException
    {
    }
    @Override public void editFamily(java.lang.String familyId, java.lang.String commandId) throws android.os.RemoteException
    {
    }
    @Override public void deleteFamily(java.lang.String familyId, java.lang.String commandId) throws android.os.RemoteException
    {
    }
    @Override
    public android.os.IBinder asBinder() {
      return null;
    }
  }
  /** Local-side IPC implementation stub class. */
  public static abstract class Stub extends android.os.Binder implements com.swaiot.aiotlib.family.IFamilyManager
  {
    private static final java.lang.String DESCRIPTOR = "com.swaiot.aiotlib.family.IFamilyManager";
    /** Construct the stub at attach it to the interface. */
    public Stub()
    {
      this.attachInterface(this, DESCRIPTOR);
    }
    /**
     * Cast an IBinder object into an com.swaiot.aiotlib.family.IFamilyManager interface,
     * generating a proxy if needed.
     */
    public static com.swaiot.aiotlib.family.IFamilyManager asInterface(android.os.IBinder obj)
    {
      if ((obj==null)) {
        return null;
      }
      android.os.IInterface iin = obj.queryLocalInterface(DESCRIPTOR);
      if (((iin!=null)&&(iin instanceof com.swaiot.aiotlib.family.IFamilyManager))) {
        return ((com.swaiot.aiotlib.family.IFamilyManager)iin);
      }
      return new com.swaiot.aiotlib.family.IFamilyManager.Stub.Proxy(obj);
    }
    @Override public android.os.IBinder asBinder()
    {
      return this;
    }
    @Override public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws android.os.RemoteException
    {
      java.lang.String descriptor = DESCRIPTOR;
      switch (code)
      {
        case INTERFACE_TRANSACTION:
        {
          reply.writeString(descriptor);
          return true;
        }
        case TRANSACTION_setCurrentFamily:
        {
          data.enforceInterface(descriptor);
          java.lang.String _arg0;
          _arg0 = data.readString();
          java.lang.String _arg1;
          _arg1 = data.readString();
          this.setCurrentFamily(_arg0, _arg1);
          reply.writeNoException();
          return true;
        }
        case TRANSACTION_addFamily:
        {
          data.enforceInterface(descriptor);
          java.lang.String _arg0;
          _arg0 = data.readString();
          this.addFamily(_arg0);
          reply.writeNoException();
          return true;
        }
        case TRANSACTION_editFamily:
        {
          data.enforceInterface(descriptor);
          java.lang.String _arg0;
          _arg0 = data.readString();
          java.lang.String _arg1;
          _arg1 = data.readString();
          this.editFamily(_arg0, _arg1);
          reply.writeNoException();
          return true;
        }
        case TRANSACTION_deleteFamily:
        {
          data.enforceInterface(descriptor);
          java.lang.String _arg0;
          _arg0 = data.readString();
          java.lang.String _arg1;
          _arg1 = data.readString();
          this.deleteFamily(_arg0, _arg1);
          reply.writeNoException();
          return true;
        }
        default:
        {
          return super.onTransact(code, data, reply, flags);
        }
      }
    }
    private static class Proxy implements com.swaiot.aiotlib.family.IFamilyManager
    {
      private android.os.IBinder mRemote;
      Proxy(android.os.IBinder remote)
      {
        mRemote = remote;
      }
      @Override public android.os.IBinder asBinder()
      {
        return mRemote;
      }
      public java.lang.String getInterfaceDescriptor()
      {
        return DESCRIPTOR;
      }
      @Override public void setCurrentFamily(java.lang.String familyId, java.lang.String commandId) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(familyId);
          _data.writeString(commandId);
          boolean _status = mRemote.transact(Stub.TRANSACTION_setCurrentFamily, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().setCurrentFamily(familyId, commandId);
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public void addFamily(java.lang.String commandId) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(commandId);
          boolean _status = mRemote.transact(Stub.TRANSACTION_addFamily, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().addFamily(commandId);
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public void editFamily(java.lang.String familyId, java.lang.String commandId) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(familyId);
          _data.writeString(commandId);
          boolean _status = mRemote.transact(Stub.TRANSACTION_editFamily, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().editFamily(familyId, commandId);
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public void deleteFamily(java.lang.String familyId, java.lang.String commandId) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(familyId);
          _data.writeString(commandId);
          boolean _status = mRemote.transact(Stub.TRANSACTION_deleteFamily, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().deleteFamily(familyId, commandId);
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      public static com.swaiot.aiotlib.family.IFamilyManager sDefaultImpl;
    }
    static final int TRANSACTION_setCurrentFamily = (android.os.IBinder.FIRST_CALL_TRANSACTION + 0);
    static final int TRANSACTION_addFamily = (android.os.IBinder.FIRST_CALL_TRANSACTION + 1);
    static final int TRANSACTION_editFamily = (android.os.IBinder.FIRST_CALL_TRANSACTION + 2);
    static final int TRANSACTION_deleteFamily = (android.os.IBinder.FIRST_CALL_TRANSACTION + 3);
    public static boolean setDefaultImpl(com.swaiot.aiotlib.family.IFamilyManager impl) {
      if (Stub.Proxy.sDefaultImpl == null && impl != null) {
        Stub.Proxy.sDefaultImpl = impl;
        return true;
      }
      return false;
    }
    public static com.swaiot.aiotlib.family.IFamilyManager getDefaultImpl() {
      return Stub.Proxy.sDefaultImpl;
    }
  }
  public void setCurrentFamily(java.lang.String familyId, java.lang.String commandId) throws android.os.RemoteException;
  public void addFamily(java.lang.String commandId) throws android.os.RemoteException;
  public void editFamily(java.lang.String familyId, java.lang.String commandId) throws android.os.RemoteException;
  public void deleteFamily(java.lang.String familyId, java.lang.String commandId) throws android.os.RemoteException;
}
