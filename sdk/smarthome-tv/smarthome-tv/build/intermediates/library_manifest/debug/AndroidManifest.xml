<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.skyworth.smarthome"
    android:versionCode="1"
    android:versionName="1.0" >

    <uses-sdk
        android:minSdkVersion="17"
        android:targetSdkVersion="29" />

    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" /> <!-- 获取网络状态改变的权限 -->
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" /> <!-- 允许应用程序改变网络状态 -->
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" /> <!-- 允许应用程序改变WIFI连接状态 -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.INTERNET" /> <!-- 允许应用程序完全使用网络 -->
    <uses-permission android:name="android.permission.BLUETOOTH" /> <!-- 获取蓝牙的权限 -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.SYSTEM_OVERLAY_WINDOW" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.BROADCAST_STICKY" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- 开启/禁用屏保权限 -->
    <uses-permission android:name="com.tianci.user.permission.READ_CONTENT" />

    <application>
        <activity
            android:name="com.skyworth.smarthome.personal.PersonalCenterActivity"
            android:launchMode="singleTask"
            android:screenOrientation="landscape" >
            <intent-filter>
                <action android:name="com.smarthome.action.PERSONAL_CENTER" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.skyworth.smarthome.personal.unbinddevice.UnbindDeviceActivity"
            android:launchMode="singleTask"
            android:screenOrientation="landscape" >
            <intent-filter>
                <action android:name="com.smarthome.action.UNBIND_DEVICE" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.skyworth.smarthome.personal.thirdaccount.ThirdAccountActivity"
            android:launchMode="singleTask"
            android:screenOrientation="landscape" >
            <intent-filter>
                <action android:name="com.smarthome.action.THRID_ACCOUNT" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.skyworth.smarthome.home.DeviceGuideAcitvity"
            android:launchMode="singleTask"
            android:screenOrientation="landscape" >
            <intent-filter>
                <action android:name="com.smarthome.action.DEVICE_GUIDE" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <service
            android:name="com.skyworth.smarthome.devices.apconfig.ApConfigService"
            android:exported="false" >
            <intent-filter>
                <action android:name="coocaa.intent.action.APCONFIG_SERVICE" />
            </intent-filter>
        </service>
        <service
            android:name="com.skyworth.smarthome.service.SmartHomeService"
            android:enabled="true"
            android:exported="true" >
            <intent-filter>
                <action android:name="com.skyworth.aiot.ConnectBinderPool" />
                <action android:name="com.skyworth.smarthome_tv.SmartHomeService" />
                <action android:name="com.skyworth.smarthome_tv.ISmartHomePushService" />
                <action android:name="com.skyworth.smarthome_tv.ISmartHomeService" />
            </intent-filter>
        </service>

        <receiver
            android:name="com.skyworth.smarthome.account.AccountReceiver"
            android:enabled="true"
            android:exported="true" >
            <intent-filter>

                <!-- 开机广播 -->
                <action android:name="com.tianci.user.account_changed" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="com.skyworth.smarthome.devices.discover.receiver.StartServiceReceiver"
            android:enabled="true"
            android:exported="true" >
            <intent-filter>

                <!-- 开机广播 -->
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="com.skyworth.broadcast.standby.quick.resume" />
                <action android:name="android.intent.action.BOOT_COMPLETED.TC" />
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
                <action android:name="com.skyworth.broadcast.screensound.open" />
                <action android:name="com.skyworth.broadcast.screensound.close" />
                <action android:name="com.tianci.ad.start_screensaver" />
                <action android:name="com.tianci.ad.exist_screensaver" />
                <action android:name="com.skyworth.screensaverexit" />
                <action android:name="com.skyworth.broadcast.recovery.start" />
            </intent-filter>
        </receiver>
        <receiver android:name="com.skyworth.smarthome.voicehandle.VoiceHandleReceiver" >
            <intent-filter>
                <action android:name="com.skyworth.srtnj.action.voice.outcmd" />
                <action android:name="com.skyworth.smarthome_tv.voicehandler" />
                <action android:name="com.skyworth.smarthome_tv.helphandler" />
            </intent-filter>
        </receiver>

        <meta-data
            android:name="DEVICE_SERVER"
            android:value="https://api-sit.skyworthiot.com/" />
        <meta-data
            android:name="HOMEPAGE_SERVER"
            android:value="http://beta-api-home.skysrt.com/" />
        <meta-data
            android:name="APPSTORE_SERVER"
            android:value="http://beta-tc.skysrt.com/appstore/appstorev3/" />
    </application>

</manifest>