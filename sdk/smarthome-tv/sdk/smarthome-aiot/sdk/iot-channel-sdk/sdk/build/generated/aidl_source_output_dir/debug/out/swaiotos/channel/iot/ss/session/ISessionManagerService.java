/*
 * This file is auto-generated.  DO NOT MODIFY.
 */
package swaiotos.channel.iot.ss.session;
public interface ISessionManagerService extends android.os.IInterface
{
/** Local-side IPC implementation stub class. */
public static abstract class Stub extends android.os.Bin<PERSON> implements swaiotos.channel.iot.ss.session.ISessionManagerService
{
private static final java.lang.String DESCRIPTOR = "swaiotos.channel.iot.ss.session.ISessionManagerService";
/** Construct the stub at attach it to the interface. */
public Stub()
{
this.attachInterface(this, DESCRIPTOR);
}
/**
 * Cast an IBinder object into an swaiotos.channel.iot.ss.session.ISessionManagerService interface,
 * generating a proxy if needed.
 */
public static swaiotos.channel.iot.ss.session.ISessionManagerService asInterface(android.os.IBinder obj)
{
if ((obj==null)) {
return null;
}
android.os.IInterface iin = obj.queryLocalInterface(DESCRIPTOR);
if (((iin!=null)&&(iin instanceof swaiotos.channel.iot.ss.session.ISessionManagerService))) {
return ((swaiotos.channel.iot.ss.session.ISessionManagerService)iin);
}
return new swaiotos.channel.iot.ss.session.ISessionManagerService.Stub.Proxy(obj);
}
@Override public android.os.IBinder asBinder()
{
return this;
}
@Override public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws android.os.RemoteException
{
java.lang.String descriptor = DESCRIPTOR;
switch (code)
{
case INTERFACE_TRANSACTION:
{
reply.writeString(descriptor);
return true;
}
case TRANSACTION_addOnMySessionUpdateListener:
{
data.enforceInterface(descriptor);
swaiotos.channel.iot.ss.session.IOnMySessionUpdateListener _arg0;
_arg0 = swaiotos.channel.iot.ss.session.IOnMySessionUpdateListener.Stub.asInterface(data.readStrongBinder());
this.addOnMySessionUpdateListener(_arg0);
reply.writeNoException();
return true;
}
case TRANSACTION_removeOnMySessionUpdateListener:
{
data.enforceInterface(descriptor);
swaiotos.channel.iot.ss.session.IOnMySessionUpdateListener _arg0;
_arg0 = swaiotos.channel.iot.ss.session.IOnMySessionUpdateListener.Stub.asInterface(data.readStrongBinder());
this.removeOnMySessionUpdateListener(_arg0);
reply.writeNoException();
return true;
}
case TRANSACTION_getMySession:
{
data.enforceInterface(descriptor);
swaiotos.channel.iot.utils.ipc.ParcelableObject _result = this.getMySession();
reply.writeNoException();
if ((_result!=null)) {
reply.writeInt(1);
_result.writeToParcel(reply, android.os.Parcelable.PARCELABLE_WRITE_RETURN_VALUE);
}
else {
reply.writeInt(0);
}
return true;
}
case TRANSACTION_addConnectedSessionOnUpdateListener:
{
data.enforceInterface(descriptor);
swaiotos.channel.iot.ss.session.IOnSessionUpdateListener _arg0;
_arg0 = swaiotos.channel.iot.ss.session.IOnSessionUpdateListener.Stub.asInterface(data.readStrongBinder());
this.addConnectedSessionOnUpdateListener(_arg0);
reply.writeNoException();
return true;
}
case TRANSACTION_removeConnectedSessionOnUpdateListener:
{
data.enforceInterface(descriptor);
swaiotos.channel.iot.ss.session.IOnSessionUpdateListener _arg0;
_arg0 = swaiotos.channel.iot.ss.session.IOnSessionUpdateListener.Stub.asInterface(data.readStrongBinder());
this.removeConnectedSessionOnUpdateListener(_arg0);
reply.writeNoException();
return true;
}
case TRANSACTION_getConnectedSession:
{
data.enforceInterface(descriptor);
swaiotos.channel.iot.utils.ipc.ParcelableObject _result = this.getConnectedSession();
reply.writeNoException();
if ((_result!=null)) {
reply.writeInt(1);
_result.writeToParcel(reply, android.os.Parcelable.PARCELABLE_WRITE_RETURN_VALUE);
}
else {
reply.writeInt(0);
}
return true;
}
case TRANSACTION_addServerSessionOnUpdateListener:
{
data.enforceInterface(descriptor);
swaiotos.channel.iot.ss.session.IOnSessionUpdateListener _arg0;
_arg0 = swaiotos.channel.iot.ss.session.IOnSessionUpdateListener.Stub.asInterface(data.readStrongBinder());
this.addServerSessionOnUpdateListener(_arg0);
reply.writeNoException();
return true;
}
case TRANSACTION_removeServerSessionOnUpdateListener:
{
data.enforceInterface(descriptor);
swaiotos.channel.iot.ss.session.IOnSessionUpdateListener _arg0;
_arg0 = swaiotos.channel.iot.ss.session.IOnSessionUpdateListener.Stub.asInterface(data.readStrongBinder());
this.removeServerSessionOnUpdateListener(_arg0);
reply.writeNoException();
return true;
}
case TRANSACTION_getServerSessions:
{
data.enforceInterface(descriptor);
java.util.List<swaiotos.channel.iot.ss.session.Session> _result = this.getServerSessions();
reply.writeNoException();
reply.writeTypedList(_result);
return true;
}
case TRANSACTION_available:
{
data.enforceInterface(descriptor);
swaiotos.channel.iot.ss.session.Session _arg0;
if ((0!=data.readInt())) {
_arg0 = swaiotos.channel.iot.ss.session.Session.CREATOR.createFromParcel(data);
}
else {
_arg0 = null;
}
java.lang.String _arg1;
_arg1 = data.readString();
boolean _result = this.available(_arg0, _arg1);
reply.writeNoException();
reply.writeInt(((_result)?(1):(0)));
return true;
}
case TRANSACTION_isConnectSSE:
{
data.enforceInterface(descriptor);
boolean _result = this.isConnectSSE();
reply.writeNoException();
reply.writeInt(((_result)?(1):(0)));
return true;
}
case TRANSACTION_getRoomDevices:
{
data.enforceInterface(descriptor);
java.util.List<swaiotos.channel.iot.ss.session.RoomDevice> _result = this.getRoomDevices();
reply.writeNoException();
reply.writeTypedList(_result);
return true;
}
case TRANSACTION_addRoomDevicesOnUpdateListener:
{
data.enforceInterface(descriptor);
swaiotos.channel.iot.ss.session.IOnRoomDevicesUpdateListener _arg0;
_arg0 = swaiotos.channel.iot.ss.session.IOnRoomDevicesUpdateListener.Stub.asInterface(data.readStrongBinder());
this.addRoomDevicesOnUpdateListener(_arg0);
reply.writeNoException();
return true;
}
case TRANSACTION_removeRoomDevicesOnUpdateListener:
{
data.enforceInterface(descriptor);
swaiotos.channel.iot.ss.session.IOnRoomDevicesUpdateListener _arg0;
_arg0 = swaiotos.channel.iot.ss.session.IOnRoomDevicesUpdateListener.Stub.asInterface(data.readStrongBinder());
this.removeRoomDevicesOnUpdateListener(_arg0);
reply.writeNoException();
return true;
}
case TRANSACTION_clearConnectedSessionByUser:
{
data.enforceInterface(descriptor);
this.clearConnectedSessionByUser();
reply.writeNoException();
return true;
}
default:
{
return super.onTransact(code, data, reply, flags);
}
}
}
private static class Proxy implements swaiotos.channel.iot.ss.session.ISessionManagerService
{
private android.os.IBinder mRemote;
Proxy(android.os.IBinder remote)
{
mRemote = remote;
}
@Override public android.os.IBinder asBinder()
{
return mRemote;
}
public java.lang.String getInterfaceDescriptor()
{
return DESCRIPTOR;
}
/**
     * Add on my session update listener.
     *
     * @param listener the listener
     * @throws Exception the exception
     */
@Override public void addOnMySessionUpdateListener(swaiotos.channel.iot.ss.session.IOnMySessionUpdateListener listener) throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
try {
_data.writeInterfaceToken(DESCRIPTOR);
_data.writeStrongBinder((((listener!=null))?(listener.asBinder()):(null)));
mRemote.transact(Stub.TRANSACTION_addOnMySessionUpdateListener, _data, _reply, 0);
_reply.readException();
}
finally {
_reply.recycle();
_data.recycle();
}
}
/**
     * Remove on my session update listener.
     *
     * @param listener the listener
     * @throws Exception the exception
     */
@Override public void removeOnMySessionUpdateListener(swaiotos.channel.iot.ss.session.IOnMySessionUpdateListener listener) throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
try {
_data.writeInterfaceToken(DESCRIPTOR);
_data.writeStrongBinder((((listener!=null))?(listener.asBinder()):(null)));
mRemote.transact(Stub.TRANSACTION_removeOnMySessionUpdateListener, _data, _reply, 0);
_reply.readException();
}
finally {
_reply.recycle();
_data.recycle();
}
}
/**
     * 获取当前设备的Session
     *
     * @return the my session
     * @throws Exception the exception
     */
@Override public swaiotos.channel.iot.utils.ipc.ParcelableObject getMySession() throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
swaiotos.channel.iot.utils.ipc.ParcelableObject _result;
try {
_data.writeInterfaceToken(DESCRIPTOR);
mRemote.transact(Stub.TRANSACTION_getMySession, _data, _reply, 0);
_reply.readException();
if ((0!=_reply.readInt())) {
_result = swaiotos.channel.iot.utils.ipc.ParcelableObject.CREATOR.createFromParcel(_reply);
}
else {
_result = null;
}
}
finally {
_reply.recycle();
_data.recycle();
}
return _result;
}
/**
     * Add on session connect update listener.
     *
     * @param listener the listener
     * @throws Exception the exception
     */
@Override public void addConnectedSessionOnUpdateListener(swaiotos.channel.iot.ss.session.IOnSessionUpdateListener listener) throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
try {
_data.writeInterfaceToken(DESCRIPTOR);
_data.writeStrongBinder((((listener!=null))?(listener.asBinder()):(null)));
mRemote.transact(Stub.TRANSACTION_addConnectedSessionOnUpdateListener, _data, _reply, 0);
_reply.readException();
}
finally {
_reply.recycle();
_data.recycle();
}
}
/**
     * Remove on session connect update listener.
     *
     * @param listener the listener
     * @throws Exception the exception
     */
@Override public void removeConnectedSessionOnUpdateListener(swaiotos.channel.iot.ss.session.IOnSessionUpdateListener listener) throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
try {
_data.writeInterfaceToken(DESCRIPTOR);
_data.writeStrongBinder((((listener!=null))?(listener.asBinder()):(null)));
mRemote.transact(Stub.TRANSACTION_removeConnectedSessionOnUpdateListener, _data, _reply, 0);
_reply.readException();
}
finally {
_reply.recycle();
_data.recycle();
}
}
/**
     * Gets the device Session for the current device connection
     *
     * @return the connected session
     * @throws Exception the exception
     */
@Override public swaiotos.channel.iot.utils.ipc.ParcelableObject getConnectedSession() throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
swaiotos.channel.iot.utils.ipc.ParcelableObject _result;
try {
_data.writeInterfaceToken(DESCRIPTOR);
mRemote.transact(Stub.TRANSACTION_getConnectedSession, _data, _reply, 0);
_reply.readException();
if ((0!=_reply.readInt())) {
_result = swaiotos.channel.iot.utils.ipc.ParcelableObject.CREATOR.createFromParcel(_reply);
}
else {
_result = null;
}
}
finally {
_reply.recycle();
_data.recycle();
}
return _result;
}
/**
     * Set the connect Session state monitor
     *
     * @param listener the listener
     * @throws Exception the exception
     */
@Override public void addServerSessionOnUpdateListener(swaiotos.channel.iot.ss.session.IOnSessionUpdateListener listener) throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
try {
_data.writeInterfaceToken(DESCRIPTOR);
_data.writeStrongBinder((((listener!=null))?(listener.asBinder()):(null)));
mRemote.transact(Stub.TRANSACTION_addServerSessionOnUpdateListener, _data, _reply, 0);
_reply.readException();
}
finally {
_reply.recycle();
_data.recycle();
}
}
/**
     * Remove on session update listener.
     *
     * @param listener the listener
     * @throws Exception the exception
     */
@Override public void removeServerSessionOnUpdateListener(swaiotos.channel.iot.ss.session.IOnSessionUpdateListener listener) throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
try {
_data.writeInterfaceToken(DESCRIPTOR);
_data.writeStrongBinder((((listener!=null))?(listener.asBinder()):(null)));
mRemote.transact(Stub.TRANSACTION_removeServerSessionOnUpdateListener, _data, _reply, 0);
_reply.readException();
}
finally {
_reply.recycle();
_data.recycle();
}
}
/**
     * Gets the Session list of the current device connected
     *
     * @return the server sessions
     * @throws Exception the exception
     */
@Override public java.util.List<swaiotos.channel.iot.ss.session.Session> getServerSessions() throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
java.util.List<swaiotos.channel.iot.ss.session.Session> _result;
try {
_data.writeInterfaceToken(DESCRIPTOR);
mRemote.transact(Stub.TRANSACTION_getServerSessions, _data, _reply, 0);
_reply.readException();
_result = _reply.createTypedArrayList(swaiotos.channel.iot.ss.session.Session.CREATOR);
}
finally {
_reply.recycle();
_data.recycle();
}
return _result;
}
@Override public boolean available(swaiotos.channel.iot.ss.session.Session session, java.lang.String channel) throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
boolean _result;
try {
_data.writeInterfaceToken(DESCRIPTOR);
if ((session!=null)) {
_data.writeInt(1);
session.writeToParcel(_data, 0);
}
else {
_data.writeInt(0);
}
_data.writeString(channel);
mRemote.transact(Stub.TRANSACTION_available, _data, _reply, 0);
_reply.readException();
_result = (0!=_reply.readInt());
}
finally {
_reply.recycle();
_data.recycle();
}
return _result;
}
@Override public boolean isConnectSSE() throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
boolean _result;
try {
_data.writeInterfaceToken(DESCRIPTOR);
mRemote.transact(Stub.TRANSACTION_isConnectSSE, _data, _reply, 0);
_reply.readException();
_result = (0!=_reply.readInt());
}
finally {
_reply.recycle();
_data.recycle();
}
return _result;
}
/**
      * Gets the Device of connection  to the current room
      *
      * @return the server room of device
       * @throws Exception the exception
    */
@Override public java.util.List<swaiotos.channel.iot.ss.session.RoomDevice> getRoomDevices() throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
java.util.List<swaiotos.channel.iot.ss.session.RoomDevice> _result;
try {
_data.writeInterfaceToken(DESCRIPTOR);
mRemote.transact(Stub.TRANSACTION_getRoomDevices, _data, _reply, 0);
_reply.readException();
_result = _reply.createTypedArrayList(swaiotos.channel.iot.ss.session.RoomDevice.CREATOR);
}
finally {
_reply.recycle();
_data.recycle();
}
return _result;
}
/**
    * Listen for changes in the number of room connections
    *
    */
@Override public void addRoomDevicesOnUpdateListener(swaiotos.channel.iot.ss.session.IOnRoomDevicesUpdateListener listener) throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
try {
_data.writeInterfaceToken(DESCRIPTOR);
_data.writeStrongBinder((((listener!=null))?(listener.asBinder()):(null)));
mRemote.transact(Stub.TRANSACTION_addRoomDevicesOnUpdateListener, _data, _reply, 0);
_reply.readException();
}
finally {
_reply.recycle();
_data.recycle();
}
}
@Override public void removeRoomDevicesOnUpdateListener(swaiotos.channel.iot.ss.session.IOnRoomDevicesUpdateListener listener) throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
try {
_data.writeInterfaceToken(DESCRIPTOR);
_data.writeStrongBinder((((listener!=null))?(listener.asBinder()):(null)));
mRemote.transact(Stub.TRANSACTION_removeRoomDevicesOnUpdateListener, _data, _reply, 0);
_reply.readException();
}
finally {
_reply.recycle();
_data.recycle();
}
}
@Override public void clearConnectedSessionByUser() throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
try {
_data.writeInterfaceToken(DESCRIPTOR);
mRemote.transact(Stub.TRANSACTION_clearConnectedSessionByUser, _data, _reply, 0);
_reply.readException();
}
finally {
_reply.recycle();
_data.recycle();
}
}
}
static final int TRANSACTION_addOnMySessionUpdateListener = (android.os.IBinder.FIRST_CALL_TRANSACTION + 0);
static final int TRANSACTION_removeOnMySessionUpdateListener = (android.os.IBinder.FIRST_CALL_TRANSACTION + 1);
static final int TRANSACTION_getMySession = (android.os.IBinder.FIRST_CALL_TRANSACTION + 2);
static final int TRANSACTION_addConnectedSessionOnUpdateListener = (android.os.IBinder.FIRST_CALL_TRANSACTION + 3);
static final int TRANSACTION_removeConnectedSessionOnUpdateListener = (android.os.IBinder.FIRST_CALL_TRANSACTION + 4);
static final int TRANSACTION_getConnectedSession = (android.os.IBinder.FIRST_CALL_TRANSACTION + 5);
static final int TRANSACTION_addServerSessionOnUpdateListener = (android.os.IBinder.FIRST_CALL_TRANSACTION + 6);
static final int TRANSACTION_removeServerSessionOnUpdateListener = (android.os.IBinder.FIRST_CALL_TRANSACTION + 7);
static final int TRANSACTION_getServerSessions = (android.os.IBinder.FIRST_CALL_TRANSACTION + 8);
static final int TRANSACTION_available = (android.os.IBinder.FIRST_CALL_TRANSACTION + 9);
static final int TRANSACTION_isConnectSSE = (android.os.IBinder.FIRST_CALL_TRANSACTION + 10);
static final int TRANSACTION_getRoomDevices = (android.os.IBinder.FIRST_CALL_TRANSACTION + 11);
static final int TRANSACTION_addRoomDevicesOnUpdateListener = (android.os.IBinder.FIRST_CALL_TRANSACTION + 12);
static final int TRANSACTION_removeRoomDevicesOnUpdateListener = (android.os.IBinder.FIRST_CALL_TRANSACTION + 13);
static final int TRANSACTION_clearConnectedSessionByUser = (android.os.IBinder.FIRST_CALL_TRANSACTION + 14);
}
/**
     * Add on my session update listener.
     *
     * @param listener the listener
     * @throws Exception the exception
     */
public void addOnMySessionUpdateListener(swaiotos.channel.iot.ss.session.IOnMySessionUpdateListener listener) throws android.os.RemoteException;
/**
     * Remove on my session update listener.
     *
     * @param listener the listener
     * @throws Exception the exception
     */
public void removeOnMySessionUpdateListener(swaiotos.channel.iot.ss.session.IOnMySessionUpdateListener listener) throws android.os.RemoteException;
/**
     * 获取当前设备的Session
     *
     * @return the my session
     * @throws Exception the exception
     */
public swaiotos.channel.iot.utils.ipc.ParcelableObject getMySession() throws android.os.RemoteException;
/**
     * Add on session connect update listener.
     *
     * @param listener the listener
     * @throws Exception the exception
     */
public void addConnectedSessionOnUpdateListener(swaiotos.channel.iot.ss.session.IOnSessionUpdateListener listener) throws android.os.RemoteException;
/**
     * Remove on session connect update listener.
     *
     * @param listener the listener
     * @throws Exception the exception
     */
public void removeConnectedSessionOnUpdateListener(swaiotos.channel.iot.ss.session.IOnSessionUpdateListener listener) throws android.os.RemoteException;
/**
     * Gets the device Session for the current device connection
     *
     * @return the connected session
     * @throws Exception the exception
     */
public swaiotos.channel.iot.utils.ipc.ParcelableObject getConnectedSession() throws android.os.RemoteException;
/**
     * Set the connect Session state monitor
     *
     * @param listener the listener
     * @throws Exception the exception
     */
public void addServerSessionOnUpdateListener(swaiotos.channel.iot.ss.session.IOnSessionUpdateListener listener) throws android.os.RemoteException;
/**
     * Remove on session update listener.
     *
     * @param listener the listener
     * @throws Exception the exception
     */
public void removeServerSessionOnUpdateListener(swaiotos.channel.iot.ss.session.IOnSessionUpdateListener listener) throws android.os.RemoteException;
/**
     * Gets the Session list of the current device connected
     *
     * @return the server sessions
     * @throws Exception the exception
     */
public java.util.List<swaiotos.channel.iot.ss.session.Session> getServerSessions() throws android.os.RemoteException;
public boolean available(swaiotos.channel.iot.ss.session.Session session, java.lang.String channel) throws android.os.RemoteException;
public boolean isConnectSSE() throws android.os.RemoteException;
/**
      * Gets the Device of connection  to the current room
      *
      * @return the server room of device
       * @throws Exception the exception
    */
public java.util.List<swaiotos.channel.iot.ss.session.RoomDevice> getRoomDevices() throws android.os.RemoteException;
/**
    * Listen for changes in the number of room connections
    *
    */
public void addRoomDevicesOnUpdateListener(swaiotos.channel.iot.ss.session.IOnRoomDevicesUpdateListener listener) throws android.os.RemoteException;
public void removeRoomDevicesOnUpdateListener(swaiotos.channel.iot.ss.session.IOnRoomDevicesUpdateListener listener) throws android.os.RemoteException;
public void clearConnectedSessionByUser() throws android.os.RemoteException;
}
