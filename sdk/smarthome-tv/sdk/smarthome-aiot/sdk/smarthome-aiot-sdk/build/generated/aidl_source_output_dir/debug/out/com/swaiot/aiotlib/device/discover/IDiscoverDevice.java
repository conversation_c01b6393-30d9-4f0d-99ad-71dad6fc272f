/*
 * This file is auto-generated.  DO NOT MODIFY.
 */
package com.swaiot.aiotlib.device.discover;
// Declare any non-default types here with import statements

public interface IDiscoverDevice extends android.os.IInterface
{
  /** Default implementation for IDiscoverDevice. */
  public static class Default implements com.swaiot.aiotlib.device.discover.IDiscoverDevice
  {
    @Override public void startDiscoverWifiDevice() throws android.os.RemoteException
    {
    }
    @Override public void stopDiscoverWifiDevice() throws android.os.RemoteException
    {
    }
    @Override public void startDiscoverNetworkDevice() throws android.os.RemoteException
    {
    }
    @Override public void stopDiscoverNetworkDevice() throws android.os.RemoteException
    {
    }
    @Override
    public android.os.IBinder asBinder() {
      return null;
    }
  }
  /** Local-side IPC implementation stub class. */
  public static abstract class Stub extends android.os.Binder implements com.swaiot.aiotlib.device.discover.IDiscoverDevice
  {
    private static final java.lang.String DESCRIPTOR = "com.swaiot.aiotlib.device.discover.IDiscoverDevice";
    /** Construct the stub at attach it to the interface. */
    public Stub()
    {
      this.attachInterface(this, DESCRIPTOR);
    }
    /**
     * Cast an IBinder object into an com.swaiot.aiotlib.device.discover.IDiscoverDevice interface,
     * generating a proxy if needed.
     */
    public static com.swaiot.aiotlib.device.discover.IDiscoverDevice asInterface(android.os.IBinder obj)
    {
      if ((obj==null)) {
        return null;
      }
      android.os.IInterface iin = obj.queryLocalInterface(DESCRIPTOR);
      if (((iin!=null)&&(iin instanceof com.swaiot.aiotlib.device.discover.IDiscoverDevice))) {
        return ((com.swaiot.aiotlib.device.discover.IDiscoverDevice)iin);
      }
      return new com.swaiot.aiotlib.device.discover.IDiscoverDevice.Stub.Proxy(obj);
    }
    @Override public android.os.IBinder asBinder()
    {
      return this;
    }
    @Override public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws android.os.RemoteException
    {
      java.lang.String descriptor = DESCRIPTOR;
      switch (code)
      {
        case INTERFACE_TRANSACTION:
        {
          reply.writeString(descriptor);
          return true;
        }
        case TRANSACTION_startDiscoverWifiDevice:
        {
          data.enforceInterface(descriptor);
          this.startDiscoverWifiDevice();
          reply.writeNoException();
          return true;
        }
        case TRANSACTION_stopDiscoverWifiDevice:
        {
          data.enforceInterface(descriptor);
          this.stopDiscoverWifiDevice();
          reply.writeNoException();
          return true;
        }
        case TRANSACTION_startDiscoverNetworkDevice:
        {
          data.enforceInterface(descriptor);
          this.startDiscoverNetworkDevice();
          reply.writeNoException();
          return true;
        }
        case TRANSACTION_stopDiscoverNetworkDevice:
        {
          data.enforceInterface(descriptor);
          this.stopDiscoverNetworkDevice();
          reply.writeNoException();
          return true;
        }
        default:
        {
          return super.onTransact(code, data, reply, flags);
        }
      }
    }
    private static class Proxy implements com.swaiot.aiotlib.device.discover.IDiscoverDevice
    {
      private android.os.IBinder mRemote;
      Proxy(android.os.IBinder remote)
      {
        mRemote = remote;
      }
      @Override public android.os.IBinder asBinder()
      {
        return mRemote;
      }
      public java.lang.String getInterfaceDescriptor()
      {
        return DESCRIPTOR;
      }
      @Override public void startDiscoverWifiDevice() throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          boolean _status = mRemote.transact(Stub.TRANSACTION_startDiscoverWifiDevice, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().startDiscoverWifiDevice();
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public void stopDiscoverWifiDevice() throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          boolean _status = mRemote.transact(Stub.TRANSACTION_stopDiscoverWifiDevice, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().stopDiscoverWifiDevice();
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public void startDiscoverNetworkDevice() throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          boolean _status = mRemote.transact(Stub.TRANSACTION_startDiscoverNetworkDevice, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().startDiscoverNetworkDevice();
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public void stopDiscoverNetworkDevice() throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          boolean _status = mRemote.transact(Stub.TRANSACTION_stopDiscoverNetworkDevice, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().stopDiscoverNetworkDevice();
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      public static com.swaiot.aiotlib.device.discover.IDiscoverDevice sDefaultImpl;
    }
    static final int TRANSACTION_startDiscoverWifiDevice = (android.os.IBinder.FIRST_CALL_TRANSACTION + 0);
    static final int TRANSACTION_stopDiscoverWifiDevice = (android.os.IBinder.FIRST_CALL_TRANSACTION + 1);
    static final int TRANSACTION_startDiscoverNetworkDevice = (android.os.IBinder.FIRST_CALL_TRANSACTION + 2);
    static final int TRANSACTION_stopDiscoverNetworkDevice = (android.os.IBinder.FIRST_CALL_TRANSACTION + 3);
    public static boolean setDefaultImpl(com.swaiot.aiotlib.device.discover.IDiscoverDevice impl) {
      if (Stub.Proxy.sDefaultImpl == null && impl != null) {
        Stub.Proxy.sDefaultImpl = impl;
        return true;
      }
      return false;
    }
    public static com.swaiot.aiotlib.device.discover.IDiscoverDevice getDefaultImpl() {
      return Stub.Proxy.sDefaultImpl;
    }
  }
  public void startDiscoverWifiDevice() throws android.os.RemoteException;
  public void stopDiscoverWifiDevice() throws android.os.RemoteException;
  public void startDiscoverNetworkDevice() throws android.os.RemoteException;
  public void stopDiscoverNetworkDevice() throws android.os.RemoteException;
}
