package com.skyworth.smarthome.service;

import com.skyworth.smarthome.common.util.DataCacheUtil;

/**
 * 启用服务管理器
 * 提供统一的接口来管理和查询启用服务的状态
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-25
 */
public class EnableServiceManager {
    
    private static final String TAG = "EnableServiceManager";
    
    private static EnableServiceManager instance;
    
    private EnableServiceManager() {
        // 私有构造函数，实现单例模式
    }
    
    /**
     * 获取EnableServiceManager的单例实例
     * 
     * @return EnableServiceManager实例
     */
    public static EnableServiceManager getInstance() {
        if (instance == null) {
            synchronized (EnableServiceManager.class) {
                if (instance == null) {
                    instance = new EnableServiceManager();
                }
            }
        }
        return instance;
    }
    
    /**
     * 检查服务是否已启用
     * 
     * @return true表示服务已启用，false表示服务未启用
     */
    public boolean isServiceEnabled() {
        return DataCacheUtil.getInstance().getBoolean(
            DataCacheUtil.KEY_ENABLE_SERVICE_STATUS, false);
    }
    
    /**
     * 设置服务启用状态
     * 
     * @param enabled true表示启用服务，false表示禁用服务
     */
    public void setServiceEnabled(boolean enabled) {
        DataCacheUtil.getInstance().putBoolean(
            DataCacheUtil.KEY_ENABLE_SERVICE_STATUS, enabled);
    }
    
    /**
     * 切换服务启用状态
     * 
     * @return 切换后的状态
     */
    public boolean toggleServiceEnabled() {
        boolean currentStatus = isServiceEnabled();
        boolean newStatus = !currentStatus;
        setServiceEnabled(newStatus);
        return newStatus;
    }
    
    /**
     * 服务状态变化监听器接口
     */
    public interface OnServiceStatusChangeListener {
        /**
         * 当服务状态发生变化时调用
         * 
         * @param enabled 新的服务状态
         */
        void onServiceStatusChanged(boolean enabled);
    }
    
    // 可以在这里添加监听器管理功能，用于通知其他组件服务状态的变化
    // private List<OnServiceStatusChangeListener> listeners = new ArrayList<>();
    
    /**
     * 示例：根据服务状态执行不同的逻辑
     */
    public void executeServiceLogic() {
        if (isServiceEnabled()) {
            // 执行启用服务时的逻辑
            performEnabledServiceActions();
        } else {
            // 执行禁用服务时的逻辑
            performDisabledServiceActions();
        }
    }
    
    private void performEnabledServiceActions() {
        // 在这里添加服务启用时需要执行的逻辑
        // 例如：启动后台服务、注册监听器等
    }
    
    private void performDisabledServiceActions() {
        // 在这里添加服务禁用时需要执行的逻辑
        // 例如：停止后台服务、注销监听器等
    }
}
