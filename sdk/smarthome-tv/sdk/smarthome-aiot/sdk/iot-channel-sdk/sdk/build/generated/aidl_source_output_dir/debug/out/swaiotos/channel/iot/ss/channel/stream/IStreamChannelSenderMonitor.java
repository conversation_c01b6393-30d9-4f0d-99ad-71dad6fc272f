/*
 * This file is auto-generated.  DO NOT MODIFY.
 */
package swaiotos.channel.iot.ss.channel.stream;
// Declare any non-default types here with import statements

public interface IStreamChannelSenderMonitor extends android.os.IInterface
{
/** Local-side IPC implementation stub class. */
public static abstract class Stub extends android.os.Binder implements swaiotos.channel.iot.ss.channel.stream.IStreamChannelSenderMonitor
{
private static final java.lang.String DESCRIPTOR = "swaiotos.channel.iot.ss.channel.stream.IStreamChannelSenderMonitor";
/** Construct the stub at attach it to the interface. */
public Stub()
{
this.attachInterface(this, DESCRIPTOR);
}
/**
 * Cast an IBinder object into an swaiotos.channel.iot.ss.channel.stream.IStreamChannelSenderMonitor interface,
 * generating a proxy if needed.
 */
public static swaiotos.channel.iot.ss.channel.stream.IStreamChannelSenderMonitor asInterface(android.os.IBinder obj)
{
if ((obj==null)) {
return null;
}
android.os.IInterface iin = obj.queryLocalInterface(DESCRIPTOR);
if (((iin!=null)&&(iin instanceof swaiotos.channel.iot.ss.channel.stream.IStreamChannelSenderMonitor))) {
return ((swaiotos.channel.iot.ss.channel.stream.IStreamChannelSenderMonitor)iin);
}
return new swaiotos.channel.iot.ss.channel.stream.IStreamChannelSenderMonitor.Stub.Proxy(obj);
}
@Override public android.os.IBinder asBinder()
{
return this;
}
@Override public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws android.os.RemoteException
{
java.lang.String descriptor = DESCRIPTOR;
switch (code)
{
case INTERFACE_TRANSACTION:
{
reply.writeString(descriptor);
return true;
}
case TRANSACTION_onAvailableChanged:
{
data.enforceInterface(descriptor);
boolean _arg0;
_arg0 = (0!=data.readInt());
this.onAvailableChanged(_arg0);
reply.writeNoException();
return true;
}
default:
{
return super.onTransact(code, data, reply, flags);
}
}
}
private static class Proxy implements swaiotos.channel.iot.ss.channel.stream.IStreamChannelSenderMonitor
{
private android.os.IBinder mRemote;
Proxy(android.os.IBinder remote)
{
mRemote = remote;
}
@Override public android.os.IBinder asBinder()
{
return mRemote;
}
public java.lang.String getInterfaceDescriptor()
{
return DESCRIPTOR;
}
@Override public void onAvailableChanged(boolean available) throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
try {
_data.writeInterfaceToken(DESCRIPTOR);
_data.writeInt(((available)?(1):(0)));
mRemote.transact(Stub.TRANSACTION_onAvailableChanged, _data, _reply, 0);
_reply.readException();
}
finally {
_reply.recycle();
_data.recycle();
}
}
}
static final int TRANSACTION_onAvailableChanged = (android.os.IBinder.FIRST_CALL_TRANSACTION + 0);
}
public void onAvailableChanged(boolean available) throws android.os.RemoteException;
}
