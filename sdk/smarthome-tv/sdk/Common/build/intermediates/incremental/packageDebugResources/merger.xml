<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/Common/src/main/res"/><source path="/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/Common/build/generated/res/rs/debug"/><source path="/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/Common/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/Common/src/main/res"/><source path="/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/Common/build/generated/res/rs/debug"/><source path="/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/Common/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/Common/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/Common/src/debug/res"/></dataSet><mergedItems/></merger>