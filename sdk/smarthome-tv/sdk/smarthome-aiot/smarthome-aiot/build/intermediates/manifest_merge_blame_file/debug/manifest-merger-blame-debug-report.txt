1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    xmlns:tools="http://schemas.android.com/tools"
4    package="com.swaiot.aiotlib"
5    android:versionCode="1"
6    android:versionName="1.0" >
7
8    <uses-sdk
8-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:20:5-59
9        android:minSdkVersion="17"
9-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:20:5-59
10        android:targetSdkVersion="29"
10-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:20:5-59
11        tools:overrideLibrary="com.midea.iot.sdk" />
11-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:20:15-56
12
13    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
13-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:5:5-81
13-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:5:22-78
14    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
14-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:6:5-75
14-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:6:22-73
15    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
15-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:7:5-78
15-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:7:22-76
16    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
16-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:8:5-80
16-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:8:22-78
17    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
17-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:9:5-79
17-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:9:22-76
18    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" /> <!-- 获取网络状态改变的权限 -->
18-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:10:5-76
18-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:10:22-73
19    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" /> <!-- 允许应用程序改变网络状态 -->
19-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:11:5-79
19-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:11:22-76
20    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" /> <!-- 允许应用程序改变WIFI连接状态 -->
20-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:6:5-75
20-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:6:22-73
21    <uses-permission android:name="android.permission.INTERNET" /> <!-- 允许应用程序完全使用网络 -->
21-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:13:5-67
21-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:13:22-64
22    <uses-permission android:name="android.permission.BLUETOOTH" />
22-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:14:5-68
22-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:14:22-65
23    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
23-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:15:5-74
23-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:15:22-71
24    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
24-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:16:5-75
24-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:16:22-72
25    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
25-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:17:5-80
25-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:17:22-78
26    <uses-permission android:name="android.permission.BROADCAST_STICKY" />
26-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:18:5-74
26-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:18:22-72
27    <uses-permission android:name="com.tianci.user.permission.READ_CONTENT" />
27-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:19:5-78
27-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:19:22-76
28
29    <application>
29-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:21:5-61:19
30        <service
30-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:22:9-31:19
31            android:name="com.swaiot.aiotlib.service.AiotService"
31-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:23:13-48
32            android:enabled="true"
32-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:24:13-35
33            android:exported="true"
33-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:25:13-36
34            android:process=":aiotlib" >
34-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:26:13-39
35            <intent-filter android:priority="1000" >
35-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:28:13-30:29
35-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:28:28-51
36                <action android:name="com.swaiot.aiotlib.service" />
36-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:29:17-69
36-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:29:25-66
37            </intent-filter>
38        </service>
39        <service
39-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:32:9-51:19
40            android:name="com.swaiot.aiotlib.service.AIOTSSClientService"
40-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:33:13-56
41            android:enabled="true"
41-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:34:13-35
42            android:exported="true"
42-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:35:13-36
43            android:process=":aiotlib" >
43-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:36:13-39
44            <intent-filter>
44-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:38:13-40:29
45                <action android:name="swaiotos.intent.action.channel.iot.SSCLIENT" />
45-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:39:17-86
45-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:39:25-83
46            </intent-filter>
47
48            <meta-data
48-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:42:13-44:51
49                android:name="ss-clientID"
49-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:43:17-43
50                android:value="client-iot-push" />
50-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:44:17-48
51            <meta-data
51-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:45:13-47:51
52                android:name="ss-clientKey"
52-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:46:17-44
53                android:value="client-iot-push" />
53-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:47:17-48
54            <meta-data
54-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:48:13-50:37
55                android:name="ss-clientVersion"
55-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:49:17-48
56                android:value="1" />
56-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:50:17-34
57        </service>
58        <service
58-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:52:9-54:24
59            android:name="com.swaiot.aiotlib.service.keep.KeepAliveHelperService"
59-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:52:18-69
60            android:process=":aiotlib" />
60-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:53:13-39
61
62        <meta-data
63            android:name="AIOT_LIB_SERVER"
63-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:56:13-43
64            android:value="https://api-sit.skyworthiot.com/" />
64-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:57:13-53
65        <meta-data
66            android:name="AIOT_PLATFORM"
66-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:59:13-41
67            android:value="PAD" />
67-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/smarthome-aiot/src/main/AndroidManifest.xml:60:13-45
68    </application>
69
70</manifest>
