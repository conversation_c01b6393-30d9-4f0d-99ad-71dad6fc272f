/*
 * This file is auto-generated.  DO NOT MODIFY.
 */
package swaiotos.channel.iot.ss.session;
public interface IOnSessionUpdateListener extends android.os.IInterface
{
/** Local-side IPC implementation stub class. */
public static abstract class Stub extends android.os.Binder implements swaiotos.channel.iot.ss.session.IOnSessionUpdateListener
{
private static final java.lang.String DESCRIPTOR = "swaiotos.channel.iot.ss.session.IOnSessionUpdateListener";
/** Construct the stub at attach it to the interface. */
public Stub()
{
this.attachInterface(this, DESCRIPTOR);
}
/**
 * Cast an IBinder object into an swaiotos.channel.iot.ss.session.IOnSessionUpdateListener interface,
 * generating a proxy if needed.
 */
public static swaiotos.channel.iot.ss.session.IOnSessionUpdateListener asInterface(android.os.IBinder obj)
{
if ((obj==null)) {
return null;
}
android.os.IInterface iin = obj.queryLocalInterface(DESCRIPTOR);
if (((iin!=null)&&(iin instanceof swaiotos.channel.iot.ss.session.IOnSessionUpdateListener))) {
return ((swaiotos.channel.iot.ss.session.IOnSessionUpdateListener)iin);
}
return new swaiotos.channel.iot.ss.session.IOnSessionUpdateListener.Stub.Proxy(obj);
}
@Override public android.os.IBinder asBinder()
{
return this;
}
@Override public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws android.os.RemoteException
{
java.lang.String descriptor = DESCRIPTOR;
switch (code)
{
case INTERFACE_TRANSACTION:
{
reply.writeString(descriptor);
return true;
}
case TRANSACTION_onSessionConnect:
{
data.enforceInterface(descriptor);
swaiotos.channel.iot.ss.session.Session _arg0;
if ((0!=data.readInt())) {
_arg0 = swaiotos.channel.iot.ss.session.Session.CREATOR.createFromParcel(data);
}
else {
_arg0 = null;
}
this.onSessionConnect(_arg0);
reply.writeNoException();
return true;
}
case TRANSACTION_onSessionUpdate:
{
data.enforceInterface(descriptor);
swaiotos.channel.iot.ss.session.Session _arg0;
if ((0!=data.readInt())) {
_arg0 = swaiotos.channel.iot.ss.session.Session.CREATOR.createFromParcel(data);
}
else {
_arg0 = null;
}
this.onSessionUpdate(_arg0);
reply.writeNoException();
return true;
}
case TRANSACTION_onSessionDisconnect:
{
data.enforceInterface(descriptor);
swaiotos.channel.iot.ss.session.Session _arg0;
if ((0!=data.readInt())) {
_arg0 = swaiotos.channel.iot.ss.session.Session.CREATOR.createFromParcel(data);
}
else {
_arg0 = null;
}
this.onSessionDisconnect(_arg0);
reply.writeNoException();
return true;
}
default:
{
return super.onTransact(code, data, reply, flags);
}
}
}
private static class Proxy implements swaiotos.channel.iot.ss.session.IOnSessionUpdateListener
{
private android.os.IBinder mRemote;
Proxy(android.os.IBinder remote)
{
mRemote = remote;
}
@Override public android.os.IBinder asBinder()
{
return mRemote;
}
public java.lang.String getInterfaceDescriptor()
{
return DESCRIPTOR;
}
@Override public void onSessionConnect(swaiotos.channel.iot.ss.session.Session session) throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
try {
_data.writeInterfaceToken(DESCRIPTOR);
if ((session!=null)) {
_data.writeInt(1);
session.writeToParcel(_data, 0);
}
else {
_data.writeInt(0);
}
mRemote.transact(Stub.TRANSACTION_onSessionConnect, _data, _reply, 0);
_reply.readException();
}
finally {
_reply.recycle();
_data.recycle();
}
}
@Override public void onSessionUpdate(swaiotos.channel.iot.ss.session.Session session) throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
try {
_data.writeInterfaceToken(DESCRIPTOR);
if ((session!=null)) {
_data.writeInt(1);
session.writeToParcel(_data, 0);
}
else {
_data.writeInt(0);
}
mRemote.transact(Stub.TRANSACTION_onSessionUpdate, _data, _reply, 0);
_reply.readException();
}
finally {
_reply.recycle();
_data.recycle();
}
}
@Override public void onSessionDisconnect(swaiotos.channel.iot.ss.session.Session session) throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
try {
_data.writeInterfaceToken(DESCRIPTOR);
if ((session!=null)) {
_data.writeInt(1);
session.writeToParcel(_data, 0);
}
else {
_data.writeInt(0);
}
mRemote.transact(Stub.TRANSACTION_onSessionDisconnect, _data, _reply, 0);
_reply.readException();
}
finally {
_reply.recycle();
_data.recycle();
}
}
}
static final int TRANSACTION_onSessionConnect = (android.os.IBinder.FIRST_CALL_TRANSACTION + 0);
static final int TRANSACTION_onSessionUpdate = (android.os.IBinder.FIRST_CALL_TRANSACTION + 1);
static final int TRANSACTION_onSessionDisconnect = (android.os.IBinder.FIRST_CALL_TRANSACTION + 2);
}
public void onSessionConnect(swaiotos.channel.iot.ss.session.Session session) throws android.os.RemoteException;
public void onSessionUpdate(swaiotos.channel.iot.ss.session.Session session) throws android.os.RemoteException;
public void onSessionDisconnect(swaiotos.channel.iot.ss.session.Session session) throws android.os.RemoteException;
}
