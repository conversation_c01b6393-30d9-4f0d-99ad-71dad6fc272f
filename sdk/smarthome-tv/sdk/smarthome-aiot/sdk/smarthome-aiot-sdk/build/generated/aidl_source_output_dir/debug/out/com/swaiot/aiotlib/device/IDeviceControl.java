/*
 * This file is auto-generated.  DO NOT MODIFY.
 */
package com.swaiot.aiotlib.device;
// Declare any non-default types here with import statements

public interface IDeviceControl extends android.os.IInterface
{
  /** Default implementation for IDeviceControl. */
  public static class Default implements com.swaiot.aiotlib.device.IDeviceControl
  {
    @Override public boolean controlDevice(java.lang.String device_id, java.lang.String param, java.lang.String commandId) throws android.os.RemoteException
    {
      return false;
    }
    @Override public void cancelNewDeviceMark(java.lang.String device_id, java.lang.String commandId) throws android.os.RemoteException
    {
    }
    @Override
    public android.os.IBinder asBinder() {
      return null;
    }
  }
  /** Local-side IPC implementation stub class. */
  public static abstract class Stub extends android.os.Binder implements com.swaiot.aiotlib.device.IDeviceControl
  {
    private static final java.lang.String DESCRIPTOR = "com.swaiot.aiotlib.device.IDeviceControl";
    /** Construct the stub at attach it to the interface. */
    public Stub()
    {
      this.attachInterface(this, DESCRIPTOR);
    }
    /**
     * Cast an IBinder object into an com.swaiot.aiotlib.device.IDeviceControl interface,
     * generating a proxy if needed.
     */
    public static com.swaiot.aiotlib.device.IDeviceControl asInterface(android.os.IBinder obj)
    {
      if ((obj==null)) {
        return null;
      }
      android.os.IInterface iin = obj.queryLocalInterface(DESCRIPTOR);
      if (((iin!=null)&&(iin instanceof com.swaiot.aiotlib.device.IDeviceControl))) {
        return ((com.swaiot.aiotlib.device.IDeviceControl)iin);
      }
      return new com.swaiot.aiotlib.device.IDeviceControl.Stub.Proxy(obj);
    }
    @Override public android.os.IBinder asBinder()
    {
      return this;
    }
    @Override public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws android.os.RemoteException
    {
      java.lang.String descriptor = DESCRIPTOR;
      switch (code)
      {
        case INTERFACE_TRANSACTION:
        {
          reply.writeString(descriptor);
          return true;
        }
        case TRANSACTION_controlDevice:
        {
          data.enforceInterface(descriptor);
          java.lang.String _arg0;
          _arg0 = data.readString();
          java.lang.String _arg1;
          _arg1 = data.readString();
          java.lang.String _arg2;
          _arg2 = data.readString();
          boolean _result = this.controlDevice(_arg0, _arg1, _arg2);
          reply.writeNoException();
          reply.writeInt(((_result)?(1):(0)));
          return true;
        }
        case TRANSACTION_cancelNewDeviceMark:
        {
          data.enforceInterface(descriptor);
          java.lang.String _arg0;
          _arg0 = data.readString();
          java.lang.String _arg1;
          _arg1 = data.readString();
          this.cancelNewDeviceMark(_arg0, _arg1);
          reply.writeNoException();
          return true;
        }
        default:
        {
          return super.onTransact(code, data, reply, flags);
        }
      }
    }
    private static class Proxy implements com.swaiot.aiotlib.device.IDeviceControl
    {
      private android.os.IBinder mRemote;
      Proxy(android.os.IBinder remote)
      {
        mRemote = remote;
      }
      @Override public android.os.IBinder asBinder()
      {
        return mRemote;
      }
      public java.lang.String getInterfaceDescriptor()
      {
        return DESCRIPTOR;
      }
      @Override public boolean controlDevice(java.lang.String device_id, java.lang.String param, java.lang.String commandId) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        boolean _result;
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(device_id);
          _data.writeString(param);
          _data.writeString(commandId);
          boolean _status = mRemote.transact(Stub.TRANSACTION_controlDevice, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            return getDefaultImpl().controlDevice(device_id, param, commandId);
          }
          _reply.readException();
          _result = (0!=_reply.readInt());
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
        return _result;
      }
      @Override public void cancelNewDeviceMark(java.lang.String device_id, java.lang.String commandId) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(device_id);
          _data.writeString(commandId);
          boolean _status = mRemote.transact(Stub.TRANSACTION_cancelNewDeviceMark, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().cancelNewDeviceMark(device_id, commandId);
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      public static com.swaiot.aiotlib.device.IDeviceControl sDefaultImpl;
    }
    static final int TRANSACTION_controlDevice = (android.os.IBinder.FIRST_CALL_TRANSACTION + 0);
    static final int TRANSACTION_cancelNewDeviceMark = (android.os.IBinder.FIRST_CALL_TRANSACTION + 1);
    public static boolean setDefaultImpl(com.swaiot.aiotlib.device.IDeviceControl impl) {
      if (Stub.Proxy.sDefaultImpl == null && impl != null) {
        Stub.Proxy.sDefaultImpl = impl;
        return true;
      }
      return false;
    }
    public static com.swaiot.aiotlib.device.IDeviceControl getDefaultImpl() {
      return Stub.Proxy.sDefaultImpl;
    }
  }
  public boolean controlDevice(java.lang.String device_id, java.lang.String param, java.lang.String commandId) throws android.os.RemoteException;
  public void cancelNewDeviceMark(java.lang.String device_id, java.lang.String commandId) throws android.os.RemoteException;
}
