/*
 * This file is auto-generated.  DO NOT MODIFY.
 */
package com.swaiot.aiotlib.scene;
// Declare any non-default types here with import statements

public interface ISceneInfo extends android.os.IInterface
{
  /** Default implementation for ISceneInfo. */
  public static class Default implements com.swaiot.aiotlib.scene.ISceneInfo
  {
    @Override public java.lang.String getSceneList(java.lang.String commandId) throws android.os.RemoteException
    {
      return null;
    }
    @Override
    public android.os.IBinder asBinder() {
      return null;
    }
  }
  /** Local-side IPC implementation stub class. */
  public static abstract class Stub extends android.os.Binder implements com.swaiot.aiotlib.scene.ISceneInfo
  {
    private static final java.lang.String DESCRIPTOR = "com.swaiot.aiotlib.scene.ISceneInfo";
    /** Construct the stub at attach it to the interface. */
    public Stub()
    {
      this.attachInterface(this, DESCRIPTOR);
    }
    /**
     * Cast an IBinder object into an com.swaiot.aiotlib.scene.ISceneInfo interface,
     * generating a proxy if needed.
     */
    public static com.swaiot.aiotlib.scene.ISceneInfo asInterface(android.os.IBinder obj)
    {
      if ((obj==null)) {
        return null;
      }
      android.os.IInterface iin = obj.queryLocalInterface(DESCRIPTOR);
      if (((iin!=null)&&(iin instanceof com.swaiot.aiotlib.scene.ISceneInfo))) {
        return ((com.swaiot.aiotlib.scene.ISceneInfo)iin);
      }
      return new com.swaiot.aiotlib.scene.ISceneInfo.Stub.Proxy(obj);
    }
    @Override public android.os.IBinder asBinder()
    {
      return this;
    }
    @Override public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws android.os.RemoteException
    {
      java.lang.String descriptor = DESCRIPTOR;
      switch (code)
      {
        case INTERFACE_TRANSACTION:
        {
          reply.writeString(descriptor);
          return true;
        }
        case TRANSACTION_getSceneList:
        {
          data.enforceInterface(descriptor);
          java.lang.String _arg0;
          _arg0 = data.readString();
          java.lang.String _result = this.getSceneList(_arg0);
          reply.writeNoException();
          reply.writeString(_result);
          return true;
        }
        default:
        {
          return super.onTransact(code, data, reply, flags);
        }
      }
    }
    private static class Proxy implements com.swaiot.aiotlib.scene.ISceneInfo
    {
      private android.os.IBinder mRemote;
      Proxy(android.os.IBinder remote)
      {
        mRemote = remote;
      }
      @Override public android.os.IBinder asBinder()
      {
        return mRemote;
      }
      public java.lang.String getInterfaceDescriptor()
      {
        return DESCRIPTOR;
      }
      @Override public java.lang.String getSceneList(java.lang.String commandId) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        java.lang.String _result;
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(commandId);
          boolean _status = mRemote.transact(Stub.TRANSACTION_getSceneList, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            return getDefaultImpl().getSceneList(commandId);
          }
          _reply.readException();
          _result = _reply.readString();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
        return _result;
      }
      public static com.swaiot.aiotlib.scene.ISceneInfo sDefaultImpl;
    }
    static final int TRANSACTION_getSceneList = (android.os.IBinder.FIRST_CALL_TRANSACTION + 0);
    public static boolean setDefaultImpl(com.swaiot.aiotlib.scene.ISceneInfo impl) {
      if (Stub.Proxy.sDefaultImpl == null && impl != null) {
        Stub.Proxy.sDefaultImpl = impl;
        return true;
      }
      return false;
    }
    public static com.swaiot.aiotlib.scene.ISceneInfo getDefaultImpl() {
      return Stub.Proxy.sDefaultImpl;
    }
  }
  public java.lang.String getSceneList(java.lang.String commandId) throws android.os.RemoteException;
}
