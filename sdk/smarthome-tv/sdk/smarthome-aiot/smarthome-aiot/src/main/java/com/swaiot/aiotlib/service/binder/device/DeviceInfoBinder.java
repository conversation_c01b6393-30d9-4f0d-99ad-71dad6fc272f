package com.swaiot.aiotlib.service.binder.device;

import android.os.RemoteException;

import com.swaiot.aiotlib.common.model.AiotConstants;
import com.swaiot.aiotlib.common.util.EmptyUtils;
import com.swaiot.aiotlib.common.util.LogUtil;
import com.swaiot.aiotlib.device.IDeviceInfo;
import com.swaiot.lib.SkyAIOTContract;

/**
 * @ClassName: DeviceInfoBinder
 * @Author: AwenZeng
 * @CreateDate: 2020/5/8 18:51
 * @Description:
 */
public class DeviceInfoBinder extends IDeviceInfo.Stub {

    @Override
    public String getDeviceList(String familyId, String commandId) throws RemoteException {
        LogUtil.androidLog("getDeviceList:"+ familyId + ":"+ commandId);
        if (EmptyUtils.isNotEmpty(familyId)) {
            SkyAIOTContract.require_resource(AiotConstants.KEY_HTTP_DEVCIE_LIST,  EmptyUtils.handleNullString(commandId),(byte)1);
            SkyAIOTContract.control_object(AiotConstants.KEY_HTTP_SET_CURRENT_FAMILY,  EmptyUtils.handleNullString(familyId), "",  EmptyUtils.handleNullString(commandId));
        } else {
            SkyAIOTContract.require_resource(AiotConstants.KEY_HTTP_DEVCIE_LIST,  EmptyUtils.handleNullString(commandId),(byte)1);
        }
        return null;
    }
}
