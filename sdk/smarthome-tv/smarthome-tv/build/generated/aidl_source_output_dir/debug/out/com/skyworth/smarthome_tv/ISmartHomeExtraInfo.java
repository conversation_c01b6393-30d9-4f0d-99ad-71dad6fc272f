/*
 * This file is auto-generated.  DO NOT MODIFY.
 */
package com.skyworth.smarthome_tv;
/**
 * Example of a secondary interface associated with a service.  (Note that
 * the interface itself doesn't impact, it is just a matter of how you
 * retrieve it from the service.)
 */
public interface ISmartHomeExtraInfo extends android.os.IInterface
{
  /** Default implementation for ISmartHomeExtraInfo. */
  public static class Default implements com.skyworth.smarthome_tv.ISmartHomeExtraInfo
  {
    @Override public java.lang.String getSmartHomeExtraInfo() throws android.os.RemoteException
    {
      return null;
    }
    @Override
    public android.os.IBinder asBinder() {
      return null;
    }
  }
  /** Local-side IPC implementation stub class. */
  public static abstract class Stub extends android.os.Binder implements com.skyworth.smarthome_tv.ISmartHomeExtraInfo
  {
    private static final java.lang.String DESCRIPTOR = "com.skyworth.smarthome_tv.ISmartHomeExtraInfo";
    /** Construct the stub at attach it to the interface. */
    public Stub()
    {
      this.attachInterface(this, DESCRIPTOR);
    }
    /**
     * Cast an IBinder object into an com.skyworth.smarthome_tv.ISmartHomeExtraInfo interface,
     * generating a proxy if needed.
     */
    public static com.skyworth.smarthome_tv.ISmartHomeExtraInfo asInterface(android.os.IBinder obj)
    {
      if ((obj==null)) {
        return null;
      }
      android.os.IInterface iin = obj.queryLocalInterface(DESCRIPTOR);
      if (((iin!=null)&&(iin instanceof com.skyworth.smarthome_tv.ISmartHomeExtraInfo))) {
        return ((com.skyworth.smarthome_tv.ISmartHomeExtraInfo)iin);
      }
      return new com.skyworth.smarthome_tv.ISmartHomeExtraInfo.Stub.Proxy(obj);
    }
    @Override public android.os.IBinder asBinder()
    {
      return this;
    }
    @Override public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws android.os.RemoteException
    {
      java.lang.String descriptor = DESCRIPTOR;
      switch (code)
      {
        case INTERFACE_TRANSACTION:
        {
          reply.writeString(descriptor);
          return true;
        }
        case TRANSACTION_getSmartHomeExtraInfo:
        {
          data.enforceInterface(descriptor);
          java.lang.String _result = this.getSmartHomeExtraInfo();
          reply.writeNoException();
          reply.writeString(_result);
          return true;
        }
        default:
        {
          return super.onTransact(code, data, reply, flags);
        }
      }
    }
    private static class Proxy implements com.skyworth.smarthome_tv.ISmartHomeExtraInfo
    {
      private android.os.IBinder mRemote;
      Proxy(android.os.IBinder remote)
      {
        mRemote = remote;
      }
      @Override public android.os.IBinder asBinder()
      {
        return mRemote;
      }
      public java.lang.String getInterfaceDescriptor()
      {
        return DESCRIPTOR;
      }
      @Override public java.lang.String getSmartHomeExtraInfo() throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        java.lang.String _result;
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          boolean _status = mRemote.transact(Stub.TRANSACTION_getSmartHomeExtraInfo, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            return getDefaultImpl().getSmartHomeExtraInfo();
          }
          _reply.readException();
          _result = _reply.readString();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
        return _result;
      }
      public static com.skyworth.smarthome_tv.ISmartHomeExtraInfo sDefaultImpl;
    }
    static final int TRANSACTION_getSmartHomeExtraInfo = (android.os.IBinder.FIRST_CALL_TRANSACTION + 0);
    public static boolean setDefaultImpl(com.skyworth.smarthome_tv.ISmartHomeExtraInfo impl) {
      if (Stub.Proxy.sDefaultImpl == null && impl != null) {
        Stub.Proxy.sDefaultImpl = impl;
        return true;
      }
      return false;
    }
    public static com.skyworth.smarthome_tv.ISmartHomeExtraInfo getDefaultImpl() {
      return Stub.Proxy.sDefaultImpl;
    }
  }
  public java.lang.String getSmartHomeExtraInfo() throws android.os.RemoteException;
}
