/*
 * This file is auto-generated.  DO NOT MODIFY.
 */
package swaiotos.channel.iot.ss.channel.stream;
public interface IStreamChannelSender extends android.os.IInterface
{
/** Local-side IPC implementation stub class. */
public static abstract class Stub extends android.os.Binder implements swaiotos.channel.iot.ss.channel.stream.IStreamChannelSender
{
private static final java.lang.String DESCRIPTOR = "swaiotos.channel.iot.ss.channel.stream.IStreamChannelSender";
/** Construct the stub at attach it to the interface. */
public Stub()
{
this.attachInterface(this, DESCRIPTOR);
}
/**
 * Cast an IBinder object into an swaiotos.channel.iot.ss.channel.stream.IStreamChannelSender interface,
 * generating a proxy if needed.
 */
public static swaiotos.channel.iot.ss.channel.stream.IStreamChannelSender asInterface(android.os.IBinder obj)
{
if ((obj==null)) {
return null;
}
android.os.IInterface iin = obj.queryLocalInterface(DESCRIPTOR);
if (((iin!=null)&&(iin instanceof swaiotos.channel.iot.ss.channel.stream.IStreamChannelSender))) {
return ((swaiotos.channel.iot.ss.channel.stream.IStreamChannelSender)iin);
}
return new swaiotos.channel.iot.ss.channel.stream.IStreamChannelSender.Stub.Proxy(obj);
}
@Override public android.os.IBinder asBinder()
{
return this;
}
@Override public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws android.os.RemoteException
{
java.lang.String descriptor = DESCRIPTOR;
switch (code)
{
case INTERFACE_TRANSACTION:
{
reply.writeString(descriptor);
return true;
}
case TRANSACTION_send:
{
data.enforceInterface(descriptor);
byte[] _arg0;
_arg0 = data.createByteArray();
this.send(_arg0);
reply.writeNoException();
return true;
}
case TRANSACTION_available:
{
data.enforceInterface(descriptor);
boolean _result = this.available();
reply.writeNoException();
reply.writeInt(((_result)?(1):(0)));
return true;
}
case TRANSACTION_setSenderMonitor:
{
data.enforceInterface(descriptor);
swaiotos.channel.iot.ss.channel.stream.IStreamChannelSenderMonitor _arg0;
_arg0 = swaiotos.channel.iot.ss.channel.stream.IStreamChannelSenderMonitor.Stub.asInterface(data.readStrongBinder());
this.setSenderMonitor(_arg0);
reply.writeNoException();
return true;
}
default:
{
return super.onTransact(code, data, reply, flags);
}
}
}
private static class Proxy implements swaiotos.channel.iot.ss.channel.stream.IStreamChannelSender
{
private android.os.IBinder mRemote;
Proxy(android.os.IBinder remote)
{
mRemote = remote;
}
@Override public android.os.IBinder asBinder()
{
return mRemote;
}
public java.lang.String getInterfaceDescriptor()
{
return DESCRIPTOR;
}
@Override public void send(byte[] data) throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
try {
_data.writeInterfaceToken(DESCRIPTOR);
_data.writeByteArray(data);
mRemote.transact(Stub.TRANSACTION_send, _data, _reply, 0);
_reply.readException();
}
finally {
_reply.recycle();
_data.recycle();
}
}
@Override public boolean available() throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
boolean _result;
try {
_data.writeInterfaceToken(DESCRIPTOR);
mRemote.transact(Stub.TRANSACTION_available, _data, _reply, 0);
_reply.readException();
_result = (0!=_reply.readInt());
}
finally {
_reply.recycle();
_data.recycle();
}
return _result;
}
@Override public void setSenderMonitor(swaiotos.channel.iot.ss.channel.stream.IStreamChannelSenderMonitor monitor) throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
try {
_data.writeInterfaceToken(DESCRIPTOR);
_data.writeStrongBinder((((monitor!=null))?(monitor.asBinder()):(null)));
mRemote.transact(Stub.TRANSACTION_setSenderMonitor, _data, _reply, 0);
_reply.readException();
}
finally {
_reply.recycle();
_data.recycle();
}
}
}
static final int TRANSACTION_send = (android.os.IBinder.FIRST_CALL_TRANSACTION + 0);
static final int TRANSACTION_available = (android.os.IBinder.FIRST_CALL_TRANSACTION + 1);
static final int TRANSACTION_setSenderMonitor = (android.os.IBinder.FIRST_CALL_TRANSACTION + 2);
}
public void send(byte[] data) throws android.os.RemoteException;
public boolean available() throws android.os.RemoteException;
public void setSenderMonitor(swaiotos.channel.iot.ss.channel.stream.IStreamChannelSenderMonitor monitor) throws android.os.RemoteException;
}
