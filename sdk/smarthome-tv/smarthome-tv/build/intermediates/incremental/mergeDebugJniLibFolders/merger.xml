<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/libs"><file name="jackson-databind-2.0.2.jar" path="/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/libs/jackson-databind-2.0.2.jar"/><file name="jackson-core-2.0.2.jar" path="/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/libs/jackson-core-2.0.2.jar"/><file name="kookongsdk_pro_162.jar" path="/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/libs/kookongsdk_pro_162.jar"/><file name="zxing.jar" path="/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/libs/zxing.jar"/><file name="armeabi-v7a/libkksdk.so" path="/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/libs/armeabi-v7a/libkksdk.so"/><file name="armeabi-v7a/libluajava.so" path="/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/libs/armeabi-v7a/libluajava.so"/><file name="app_sdk_data.jar" path="/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/libs/app_sdk_data.jar"/><file name="jackson-annotations-2.0.2.jar" path="/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/libs/jackson-annotations-2.0.2.jar"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/debug/jniLibs"/></dataSet></merger>