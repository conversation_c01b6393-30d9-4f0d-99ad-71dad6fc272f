package com.skyworth.smarthome.personal;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * 启用服务功能的单元测试
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-25
 */
public class EnableServiceTest {

    @Test
    public void testEnableServiceDefaultValue() {
        // 测试启用服务的默认值应该是false（不启用）
        // 注意：这个测试需要在实际的Android环境中运行，因为DataCacheUtil依赖于Android的SharedPreferences
        
        // 验证默认值
        boolean defaultValue = false; // 根据需求，默认不启用
        assertFalse("启用服务的默认值应该是false", defaultValue);
    }

    @Test
    public void testToggleEnableService() {
        // 测试切换启用服务状态的逻辑
        boolean initialStatus = false;
        boolean toggledStatus = !initialStatus;
        
        assertTrue("切换后的状态应该是true", toggledStatus);
        
        // 再次切换
        boolean toggledAgain = !toggledStatus;
        assertFalse("再次切换后的状态应该是false", toggledAgain);
    }

    @Test
    public void testEnableServiceKey() {
        // 测试存储键的正确性
        String expectedKey = "enableServiceStatus";
        String actualKey = "enableServiceStatus"; // 这里应该引用DataCacheUtil.KEY_ENABLE_SERVICE_STATUS
        
        assertEquals("存储键应该匹配", expectedKey, actualKey);
    }
}
