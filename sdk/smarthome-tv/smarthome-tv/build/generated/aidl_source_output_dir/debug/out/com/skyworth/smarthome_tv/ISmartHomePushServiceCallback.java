/*
 * This file is auto-generated.  DO NOT MODIFY.
 */
package com.skyworth.smarthome_tv;
/**
 * Example of a callback interface used by IRemoteService to send
 * synchronous notifications back to its clients.  Note that this is a
 * one-way interface so the server does not block waiting for the client.
 */
public interface ISmartHomePushServiceCallback extends android.os.IInterface
{
  /** Default implementation for ISmartHomePushServiceCallback. */
  public static class Default implements com.skyworth.smarthome_tv.ISmartHomePushServiceCallback
  {
    /**
         * Called when the service has a new value for you.
         */
    @Override public void onReceivedData(java.lang.String event, java.lang.String data) throws android.os.RemoteException
    {
    }
    @Override
    public android.os.IBinder asBinder() {
      return null;
    }
  }
  /** Local-side IPC implementation stub class. */
  public static abstract class Stub extends android.os.Binder implements com.skyworth.smarthome_tv.ISmartHomePushServiceCallback
  {
    private static final java.lang.String DESCRIPTOR = "com.skyworth.smarthome_tv.ISmartHomePushServiceCallback";
    /** Construct the stub at attach it to the interface. */
    public Stub()
    {
      this.attachInterface(this, DESCRIPTOR);
    }
    /**
     * Cast an IBinder object into an com.skyworth.smarthome_tv.ISmartHomePushServiceCallback interface,
     * generating a proxy if needed.
     */
    public static com.skyworth.smarthome_tv.ISmartHomePushServiceCallback asInterface(android.os.IBinder obj)
    {
      if ((obj==null)) {
        return null;
      }
      android.os.IInterface iin = obj.queryLocalInterface(DESCRIPTOR);
      if (((iin!=null)&&(iin instanceof com.skyworth.smarthome_tv.ISmartHomePushServiceCallback))) {
        return ((com.skyworth.smarthome_tv.ISmartHomePushServiceCallback)iin);
      }
      return new com.skyworth.smarthome_tv.ISmartHomePushServiceCallback.Stub.Proxy(obj);
    }
    @Override public android.os.IBinder asBinder()
    {
      return this;
    }
    @Override public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws android.os.RemoteException
    {
      java.lang.String descriptor = DESCRIPTOR;
      switch (code)
      {
        case INTERFACE_TRANSACTION:
        {
          reply.writeString(descriptor);
          return true;
        }
        case TRANSACTION_onReceivedData:
        {
          data.enforceInterface(descriptor);
          java.lang.String _arg0;
          _arg0 = data.readString();
          java.lang.String _arg1;
          _arg1 = data.readString();
          this.onReceivedData(_arg0, _arg1);
          return true;
        }
        default:
        {
          return super.onTransact(code, data, reply, flags);
        }
      }
    }
    private static class Proxy implements com.skyworth.smarthome_tv.ISmartHomePushServiceCallback
    {
      private android.os.IBinder mRemote;
      Proxy(android.os.IBinder remote)
      {
        mRemote = remote;
      }
      @Override public android.os.IBinder asBinder()
      {
        return mRemote;
      }
      public java.lang.String getInterfaceDescriptor()
      {
        return DESCRIPTOR;
      }
      /**
           * Called when the service has a new value for you.
           */
      @Override public void onReceivedData(java.lang.String event, java.lang.String data) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(event);
          _data.writeString(data);
          boolean _status = mRemote.transact(Stub.TRANSACTION_onReceivedData, _data, null, android.os.IBinder.FLAG_ONEWAY);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().onReceivedData(event, data);
            return;
          }
        }
        finally {
          _data.recycle();
        }
      }
      public static com.skyworth.smarthome_tv.ISmartHomePushServiceCallback sDefaultImpl;
    }
    static final int TRANSACTION_onReceivedData = (android.os.IBinder.FIRST_CALL_TRANSACTION + 0);
    public static boolean setDefaultImpl(com.skyworth.smarthome_tv.ISmartHomePushServiceCallback impl) {
      if (Stub.Proxy.sDefaultImpl == null && impl != null) {
        Stub.Proxy.sDefaultImpl = impl;
        return true;
      }
      return false;
    }
    public static com.skyworth.smarthome_tv.ISmartHomePushServiceCallback getDefaultImpl() {
      return Stub.Proxy.sDefaultImpl;
    }
  }
  /**
       * Called when the service has a new value for you.
       */
  public void onReceivedData(java.lang.String event, java.lang.String data) throws android.os.RemoteException;
}
