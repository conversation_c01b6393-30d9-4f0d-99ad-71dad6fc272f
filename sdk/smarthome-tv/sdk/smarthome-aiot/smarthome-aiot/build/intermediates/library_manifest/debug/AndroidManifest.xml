<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.swaiot.aiotlib"
    android:versionCode="1"
    android:versionName="1.0" >

    <uses-sdk
        android:minSdkVersion="17"
        android:targetSdkVersion="29"
        tools:overrideLibrary="com.midea.iot.sdk" />

    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" /> <!-- 获取网络状态改变的权限 -->
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" /> <!-- 允许应用程序改变网络状态 -->
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" /> <!-- 允许应用程序改变WIFI连接状态 -->
    <uses-permission android:name="android.permission.INTERNET" /> <!-- 允许应用程序完全使用网络 -->
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.BROADCAST_STICKY" />
    <uses-permission android:name="com.tianci.user.permission.READ_CONTENT" />

    <application>
        <service
            android:name="com.swaiot.aiotlib.service.AiotService"
            android:enabled="true"
            android:exported="true"
            android:process=":aiotlib" >
            <intent-filter android:priority="1000" >
                <action android:name="com.swaiot.aiotlib.service" />
            </intent-filter>
        </service>
        <service
            android:name="com.swaiot.aiotlib.service.AIOTSSClientService"
            android:enabled="true"
            android:exported="true"
            android:process=":aiotlib" >
            <intent-filter>
                <action android:name="swaiotos.intent.action.channel.iot.SSCLIENT" />
            </intent-filter>

            <meta-data
                android:name="ss-clientID"
                android:value="client-iot-push" />
            <meta-data
                android:name="ss-clientKey"
                android:value="client-iot-push" />
            <meta-data
                android:name="ss-clientVersion"
                android:value="1" />
        </service>
        <service
            android:name="com.swaiot.aiotlib.service.keep.KeepAliveHelperService"
            android:process=":aiotlib" />

        <meta-data
            android:name="AIOT_LIB_SERVER"
            android:value="https://api-sit.skyworthiot.com/" />
        <meta-data
            android:name="AIOT_PLATFORM"
            android:value="PAD" />
    </application>

</manifest>