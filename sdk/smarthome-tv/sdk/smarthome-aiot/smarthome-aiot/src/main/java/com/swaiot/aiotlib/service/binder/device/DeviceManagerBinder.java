package com.swaiot.aiotlib.service.binder.device;

import android.os.RemoteException;

import com.swaiot.aiotlib.device.IDeviceManager;

/**
 * @ClassName: DeviceManager
 * @Author: AwenZeng
 * @CreateDate: 2020/5/8 18:51
 * @Description:
 */
public class DeviceManagerBinder extends IDeviceManager.Stub {

    @Override
    public void getBindStatus(String deviceParam, String commandId) throws RemoteException {

    }

    @Override
    public void bindDevice(String deviceId, String param, String commandId) throws RemoteException {

    }

    @Override
    public void unBindDevice(String devicesId, String commandId) throws RemoteException {

    }

    @Override
    public void forceUnBindDevice(String deviceId, String commandId) throws RemoteException {

    }
}
