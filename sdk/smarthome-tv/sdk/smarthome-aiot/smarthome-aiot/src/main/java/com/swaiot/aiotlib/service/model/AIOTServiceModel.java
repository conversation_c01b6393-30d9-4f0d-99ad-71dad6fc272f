package com.swaiot.aiotlib.service.model;


import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.swaiot.aiotlib.AIOTLib;
import com.swaiot.aiotlib.AiotLibSDK;
import com.swaiot.aiotlib.BuildConfig;
import com.swaiot.aiotlib.account.IAiotAccountManager;
import com.swaiot.aiotlib.common.bean.BaiduResultBean;
import com.swaiot.aiotlib.common.bean.SsePushBean;
import com.swaiot.aiotlib.common.http.AIOTHttpManager;
import com.swaiot.aiotlib.common.http.base.HttpApi;
import com.swaiot.aiotlib.common.http.base.HttpResult;
import com.swaiot.aiotlib.common.http.base.HttpSubscribe;
import com.swaiot.aiotlib.common.http.base.HttpThrowable;
import com.swaiot.aiotlib.common.model.AiotAppData;
import com.swaiot.aiotlib.common.model.AiotConstants;
import com.swaiot.aiotlib.common.model.AiotUserInfo;
import com.swaiot.aiotlib.common.util.EmptyUtils;
import com.swaiot.aiotlib.common.util.LogUtil;
import com.swaiot.aiotlib.common.util.ThreadManager;
import com.swaiot.aiotlib.service.AiotService;
import com.swaiot.lib.SkyAIOTContract;

import java.util.HashMap;

import swaiotos.channel.iot.IOTChannel;
import swaiotos.channel.iot.ss.SSChannel;

/**
 * @ClassName: AIOTServiceModel
 * @Author: AwenZeng
 * @CreateDate: 2020/4/2 15:07
 * @Description:
 */
public class AIOTServiceModel implements IAIOTServiceModel, NetworkModel.NetworkChangeListener {
    private static final String TAG = "AIOTServiceModel";
    private byte[] lock = new byte[0];
    private SkyAiotLibCallback mLibCallback;
    private INetworkModel mNetworkModel;

    public AIOTServiceModel() {
    }

    public AIOTServiceModel(SkyAiotLibCallback libCallback) {
        mLibCallback = libCallback;
        mNetworkModel = new NetworkModel(AiotAppData.getInstance().getContext());
        mNetworkModel.setNetworkChangeListener(this);
    }

    @Override
    public void initSID() {
        if (AIOTLib.getDefault().getPlatform() != AiotLibSDK.Platform.PHONE&&(EmptyUtils.isNotEmpty(AiotAppData.getInstance().getScreenSID()) || EmptyUtils.isEmpty(AiotAppData.getInstance().getContext()))){//SID不为空的时候，才去拿
            onInitScreenId(AiotAppData.getInstance().getScreenSID());
           return;
       }
        LogUtil.androidLog("SmartScreen SID init");
        ThreadManager.getInstance().ioThread(new Runnable() {
            @Override
            public void run() {
                String pkgName = "";
                if (AIOTLib.getDefault().getPlatform() == AiotLibSDK.Platform.PHONE) {
                    if ("com.skyworth.smartsystem.valarm".equals(AiotAppData.getInstance().getContext().getPackageName())) {
                        pkgName = "com.skyworth.smartsystem.valarm";
                    } else {
                        pkgName = "com.skyworth.smartsystem.vhome";
                    }
                } else {
                    pkgName = "swaiotos.channel.iot";
                }
                IOTChannel.mananger.open(AiotAppData.getInstance().getContext(),
                        pkgName, new IOTChannel.OpenCallback() {
                            @Override
                            public void onConntected(SSChannel channel) {
                                try {

                                    String sid = channel.getSessionManager().getMySession().getId();
                                    LogUtil.androidLog("SmartScreen-SID:" + sid);
                                    if (EmptyUtils.isNotEmpty(sid)) {
                                        AiotAppData.getInstance().setScreenSID(sid);
                                        ThreadManager.getInstance().uiThread(new Runnable() {
                                            @Override
                                            public void run() {
                                                registerSSE(false);
                                            }
                                        }, 1500);
                                        onInitScreenId(sid);
                                    }
                                } catch (Exception e) {
                                    LogUtil.androidLog("SmartScreen-SID:" + e.toString());
                                    e.printStackTrace();
                                }
                            }

                            @Override
                            public void onError(String s) {

                            }
                        });
            }
        });
    }

    /**
     * 初始公共库
     */
    @Override
    public void initRustLib() {
        LogUtil.androidLog("Native init：userID:" + AiotUserInfo.getInstance().getUserID() + " token:" + AiotUserInfo.getInstance().getToken()
                + " appKey:" + AIOTLib.getDefault().getAppKey() + " appSecret:" + AIOTLib.getDefault().getAppSecret());
        ThreadManager.getInstance().ioThread(new Runnable() {
            @Override
            public void run() {
                String sdk_info = SkyAIOTContract.sdk_info();
                LogUtil.androidLog("initRustLib sdk_info:"+sdk_info);
                String userID = AiotUserInfo.getInstance().getUserID();
                String token = AiotUserInfo.getInstance().getToken();
                if (EmptyUtils.isEmpty(userID) || EmptyUtils.isEmpty(token)) {
                    userID = "";
                    token = "";
                }

                HashMap<String, Object> options = new HashMap();
                options.put("storage_path", AiotAppData.getInstance().getContext().getFilesDir().getPath());

                String activeId = AIOTLib.getDefault().getHeaderMap().get("activation_id");
                if (!TextUtils.isEmpty(activeId)) {
                    // if "activeId" is null, native can not parse json, so not push
                    options.put("filter_device_list", new String[]{activeId});
                }
                options.put("debug_server", BuildConfig.DEBUG);
                options.put("platform", AIOTLib.getDefault().getPlatform().toString());
                options.put("app_id", android.os.Process.myPid());
                options.put("device_id", activeId);
                options.put("server_host", BuildConfig.SERVER_HOST);
                SkyAIOTContract.do_init(
                        userID,
                        token,
                        EmptyUtils.handleNullString(AIOTLib.getDefault().getAppKey()),
                        EmptyUtils.handleNullString(AIOTLib.getDefault().getAppSecret()),
                        EmptyUtils.handleNullString(new Gson().toJson(options)),
                        mLibCallback);
                if (AIOTLib.getDefault().getPlatform() == AiotLibSDK.Platform.TV) {
                    String[] watchs = new String[]{AiotConstants.KEY_HTTP_HOME_STATUS, AiotConstants.KEY_HTTP_DEVICE_NOTIFY, AiotConstants.KEY_HTTP_CURRENT_FAMILY, AiotConstants.KEY_HTTP_DEVCIE_LIST,AiotConstants.KEY_HTTP_SCENE_LIST};
                    SkyAIOTContract.watch_resources(watchs);
                }
            }
        });
    }

    @Override
    public void onInitScreenId(String screenId) {
        if (EmptyUtils.isNotEmpty(AiotService.getAiotPushServiceBinder())) {
            try {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("screen_id", screenId);
                AiotService.getAiotPushServiceBinder().onInit(jsonObject.toJSONString());
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void registerIotSse() {
        HttpApi.getInstance().request(AIOTHttpManager.SERVICE.registerScreenIot(), new HttpSubscribe<HttpResult>() {
            @Override
            public void onSuccess(HttpResult result) {

            }

            @Override
            public void onError(HttpThrowable error) {

            }
        });
    }

    @Override
    public void unRegisterIotSse() {
        HttpApi.getInstance().request(AIOTHttpManager.SERVICE.unRegisterScreenIot(), new HttpSubscribe<HttpResult>() {
            @Override
            public void onSuccess(HttpResult result) {

            }

            @Override
            public void onError(HttpThrowable error) {

            }
        });
    }

    @Override
    public void deviceListForSync() {
        DeviceListSyncHelper.deviceListForSync();
    }

    /**
     * 注册SSE
     */
    @Override
    public void registerSSE(boolean isNeedUnRegister) {
        if (IAiotAccountManager.INSTANCE.hasLogin()) {
            registerIotSse();
            deviceListForSync();
        } else {
            if (isNeedUnRegister) {
                unRegisterIotSse();
            }
        }
    }

    @Override
    public void registerNetworkReceiver() {
        mNetworkModel.registerNetReceiver();
    }

    @Override
    public void unRegisterNetworkReceiver() {
        mNetworkModel.unRegisterNetReceiver();
    }

    @Override
    public void noticeRustLibAccountChange() {
        if (EmptyUtils.isNotEmpty(AiotService.getAiotPushServiceBinder())) {
            try {
                AiotService.getAiotPushServiceBinder().onAccountChange();
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("uid", AiotUserInfo.getInstance().getUserID());
        jsonObject.put("ak", AiotUserInfo.getInstance().getToken());
        SkyAIOTContract.on_app_status(AiotConstants.KEY_ACCOUNT_INFO, jsonObject.toJSONString());
    }

    @Override
    public void noticeRustLibSsePushMessage(SsePushBean ssePushBean) {
        Log.d(TAG, "noticeRustLibSsePushMessage: "+ssePushBean.getData());
        synchronized (lock) {
            if (EmptyUtils.isNotEmpty(AiotService.getAiotPushServiceBinder())) {
                try {
                    AiotService.getAiotPushServiceBinder().onReceiveData(ssePushBean.getEvent(), ssePushBean.getData());
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
                ThreadManager.getInstance().ioThread(new Runnable() {
                    @Override
                    public void run() {
                        SkyAIOTContract.on_event_data(EmptyUtils.handleNullString(ssePushBean.getId()), EmptyUtils.handleNullString(ssePushBean.getEvent()), EmptyUtils.handleNullString(ssePushBean.getData()));
                    }
                });
            }
        }
    }

    @Override
    public void onDestroy() {
        IOTChannel.mananger.close();
        unRegisterNetworkReceiver();
    }

    @Override
    public void onNetworkChanged(boolean isConnected, String ssid) {
        Log.i(TAG, "onNetworkChanged, isConnected: " + isConnected + ", ssid: " + ssid);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("ssid", ssid);
        jsonObject.put("is_connect", isConnected);
        SkyAIOTContract.on_app_status(AiotConstants.KEY_NETWORK_INFO, jsonObject.toJSONString());
    }
}
