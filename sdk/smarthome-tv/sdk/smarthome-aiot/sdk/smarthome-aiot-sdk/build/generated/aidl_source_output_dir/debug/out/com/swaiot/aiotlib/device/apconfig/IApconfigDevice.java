/*
 * This file is auto-generated.  DO NOT MODIFY.
 */
package com.swaiot.aiotlib.device.apconfig;
// Declare any non-default types here with import statements

public interface IApconfigDevice extends android.os.IInterface
{
  /** Default implementation for IApconfigDevice. */
  public static class Default implements com.swaiot.aiotlib.device.apconfig.IApconfigDevice
  {
    @Override public void startApconfig(java.lang.String wifiSSID, java.lang.String wifiPassword, java.lang.String deviceSSID, java.lang.String param) throws android.os.RemoteException
    {
    }
    @Override public void stopApconfig(java.lang.String deviceSSID) throws android.os.RemoteException
    {
    }
    @Override public void onNetworkChange(boolean isConnect, java.lang.String ssid) throws android.os.RemoteException
    {
    }
    @Override
    public android.os.IBinder asBinder() {
      return null;
    }
  }
  /** Local-side IPC implementation stub class. */
  public static abstract class Stub extends android.os.Binder implements com.swaiot.aiotlib.device.apconfig.IApconfigDevice
  {
    private static final java.lang.String DESCRIPTOR = "com.swaiot.aiotlib.device.apconfig.IApconfigDevice";
    /** Construct the stub at attach it to the interface. */
    public Stub()
    {
      this.attachInterface(this, DESCRIPTOR);
    }
    /**
     * Cast an IBinder object into an com.swaiot.aiotlib.device.apconfig.IApconfigDevice interface,
     * generating a proxy if needed.
     */
    public static com.swaiot.aiotlib.device.apconfig.IApconfigDevice asInterface(android.os.IBinder obj)
    {
      if ((obj==null)) {
        return null;
      }
      android.os.IInterface iin = obj.queryLocalInterface(DESCRIPTOR);
      if (((iin!=null)&&(iin instanceof com.swaiot.aiotlib.device.apconfig.IApconfigDevice))) {
        return ((com.swaiot.aiotlib.device.apconfig.IApconfigDevice)iin);
      }
      return new com.swaiot.aiotlib.device.apconfig.IApconfigDevice.Stub.Proxy(obj);
    }
    @Override public android.os.IBinder asBinder()
    {
      return this;
    }
    @Override public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws android.os.RemoteException
    {
      java.lang.String descriptor = DESCRIPTOR;
      switch (code)
      {
        case INTERFACE_TRANSACTION:
        {
          reply.writeString(descriptor);
          return true;
        }
        case TRANSACTION_startApconfig:
        {
          data.enforceInterface(descriptor);
          java.lang.String _arg0;
          _arg0 = data.readString();
          java.lang.String _arg1;
          _arg1 = data.readString();
          java.lang.String _arg2;
          _arg2 = data.readString();
          java.lang.String _arg3;
          _arg3 = data.readString();
          this.startApconfig(_arg0, _arg1, _arg2, _arg3);
          reply.writeNoException();
          return true;
        }
        case TRANSACTION_stopApconfig:
        {
          data.enforceInterface(descriptor);
          java.lang.String _arg0;
          _arg0 = data.readString();
          this.stopApconfig(_arg0);
          reply.writeNoException();
          return true;
        }
        case TRANSACTION_onNetworkChange:
        {
          data.enforceInterface(descriptor);
          boolean _arg0;
          _arg0 = (0!=data.readInt());
          java.lang.String _arg1;
          _arg1 = data.readString();
          this.onNetworkChange(_arg0, _arg1);
          reply.writeNoException();
          return true;
        }
        default:
        {
          return super.onTransact(code, data, reply, flags);
        }
      }
    }
    private static class Proxy implements com.swaiot.aiotlib.device.apconfig.IApconfigDevice
    {
      private android.os.IBinder mRemote;
      Proxy(android.os.IBinder remote)
      {
        mRemote = remote;
      }
      @Override public android.os.IBinder asBinder()
      {
        return mRemote;
      }
      public java.lang.String getInterfaceDescriptor()
      {
        return DESCRIPTOR;
      }
      @Override public void startApconfig(java.lang.String wifiSSID, java.lang.String wifiPassword, java.lang.String deviceSSID, java.lang.String param) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(wifiSSID);
          _data.writeString(wifiPassword);
          _data.writeString(deviceSSID);
          _data.writeString(param);
          boolean _status = mRemote.transact(Stub.TRANSACTION_startApconfig, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().startApconfig(wifiSSID, wifiPassword, deviceSSID, param);
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public void stopApconfig(java.lang.String deviceSSID) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(deviceSSID);
          boolean _status = mRemote.transact(Stub.TRANSACTION_stopApconfig, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().stopApconfig(deviceSSID);
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public void onNetworkChange(boolean isConnect, java.lang.String ssid) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeInt(((isConnect)?(1):(0)));
          _data.writeString(ssid);
          boolean _status = mRemote.transact(Stub.TRANSACTION_onNetworkChange, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().onNetworkChange(isConnect, ssid);
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      public static com.swaiot.aiotlib.device.apconfig.IApconfigDevice sDefaultImpl;
    }
    static final int TRANSACTION_startApconfig = (android.os.IBinder.FIRST_CALL_TRANSACTION + 0);
    static final int TRANSACTION_stopApconfig = (android.os.IBinder.FIRST_CALL_TRANSACTION + 1);
    static final int TRANSACTION_onNetworkChange = (android.os.IBinder.FIRST_CALL_TRANSACTION + 2);
    public static boolean setDefaultImpl(com.swaiot.aiotlib.device.apconfig.IApconfigDevice impl) {
      if (Stub.Proxy.sDefaultImpl == null && impl != null) {
        Stub.Proxy.sDefaultImpl = impl;
        return true;
      }
      return false;
    }
    public static com.swaiot.aiotlib.device.apconfig.IApconfigDevice getDefaultImpl() {
      return Stub.Proxy.sDefaultImpl;
    }
  }
  public void startApconfig(java.lang.String wifiSSID, java.lang.String wifiPassword, java.lang.String deviceSSID, java.lang.String param) throws android.os.RemoteException;
  public void stopApconfig(java.lang.String deviceSSID) throws android.os.RemoteException;
  public void onNetworkChange(boolean isConnect, java.lang.String ssid) throws android.os.RemoteException;
}
