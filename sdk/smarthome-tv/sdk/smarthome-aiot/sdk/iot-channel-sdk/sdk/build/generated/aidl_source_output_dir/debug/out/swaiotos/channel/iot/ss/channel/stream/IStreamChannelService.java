/*
 * This file is auto-generated.  DO NOT MODIFY.
 */
package swaiotos.channel.iot.ss.channel.stream;
public interface IStreamChannelService extends android.os.IInterface
{
/** Local-side IPC implementation stub class. */
public static abstract class Stub extends android.os.Binder implements swaiotos.channel.iot.ss.channel.stream.IStreamChannelService
{
private static final java.lang.String DESCRIPTOR = "swaiotos.channel.iot.ss.channel.stream.IStreamChannelService";
/** Construct the stub at attach it to the interface. */
public Stub()
{
this.attachInterface(this, DESCRIPTOR);
}
/**
 * Cast an IBinder object into an swaiotos.channel.iot.ss.channel.stream.IStreamChannelService interface,
 * generating a proxy if needed.
 */
public static swaiotos.channel.iot.ss.channel.stream.IStreamChannelService asInterface(android.os.IBinder obj)
{
if ((obj==null)) {
return null;
}
android.os.IInterface iin = obj.queryLocalInterface(DESCRIPTOR);
if (((iin!=null)&&(iin instanceof swaiotos.channel.iot.ss.channel.stream.IStreamChannelService))) {
return ((swaiotos.channel.iot.ss.channel.stream.IStreamChannelService)iin);
}
return new swaiotos.channel.iot.ss.channel.stream.IStreamChannelService.Stub.Proxy(obj);
}
@Override public android.os.IBinder asBinder()
{
return this;
}
@Override public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws android.os.RemoteException
{
java.lang.String descriptor = DESCRIPTOR;
switch (code)
{
case INTERFACE_TRANSACTION:
{
reply.writeString(descriptor);
return true;
}
case TRANSACTION_open:
{
data.enforceInterface(descriptor);
swaiotos.channel.iot.ss.channel.stream.IStreamChannelReceiver _arg0;
_arg0 = swaiotos.channel.iot.ss.channel.stream.IStreamChannelReceiver.Stub.asInterface(data.readStrongBinder());
int _result = this.open(_arg0);
reply.writeNoException();
reply.writeInt(_result);
return true;
}
case TRANSACTION_close:
{
data.enforceInterface(descriptor);
int _arg0;
_arg0 = data.readInt();
this.close(_arg0);
reply.writeNoException();
return true;
}
case TRANSACTION_openSender:
{
data.enforceInterface(descriptor);
swaiotos.channel.iot.ss.session.Session _arg0;
if ((0!=data.readInt())) {
_arg0 = swaiotos.channel.iot.ss.session.Session.CREATOR.createFromParcel(data);
}
else {
_arg0 = null;
}
int _arg1;
_arg1 = data.readInt();
swaiotos.channel.iot.ss.channel.stream.IStreamChannelSender _result = this.openSender(_arg0, _arg1);
reply.writeNoException();
reply.writeStrongBinder((((_result!=null))?(_result.asBinder()):(null)));
return true;
}
case TRANSACTION_closeSender:
{
data.enforceInterface(descriptor);
swaiotos.channel.iot.ss.channel.stream.IStreamChannelSender _arg0;
_arg0 = swaiotos.channel.iot.ss.channel.stream.IStreamChannelSender.Stub.asInterface(data.readStrongBinder());
this.closeSender(_arg0);
reply.writeNoException();
return true;
}
default:
{
return super.onTransact(code, data, reply, flags);
}
}
}
private static class Proxy implements swaiotos.channel.iot.ss.channel.stream.IStreamChannelService
{
private android.os.IBinder mRemote;
Proxy(android.os.IBinder remote)
{
mRemote = remote;
}
@Override public android.os.IBinder asBinder()
{
return mRemote;
}
public java.lang.String getInterfaceDescriptor()
{
return DESCRIPTOR;
}
@Override public int open(swaiotos.channel.iot.ss.channel.stream.IStreamChannelReceiver stream) throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
int _result;
try {
_data.writeInterfaceToken(DESCRIPTOR);
_data.writeStrongBinder((((stream!=null))?(stream.asBinder()):(null)));
mRemote.transact(Stub.TRANSACTION_open, _data, _reply, 0);
_reply.readException();
_result = _reply.readInt();
}
finally {
_reply.recycle();
_data.recycle();
}
return _result;
}
@Override public void close(int channelId) throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
try {
_data.writeInterfaceToken(DESCRIPTOR);
_data.writeInt(channelId);
mRemote.transact(Stub.TRANSACTION_close, _data, _reply, 0);
_reply.readException();
}
finally {
_reply.recycle();
_data.recycle();
}
}
@Override public swaiotos.channel.iot.ss.channel.stream.IStreamChannelSender openSender(swaiotos.channel.iot.ss.session.Session session, int channelId) throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
swaiotos.channel.iot.ss.channel.stream.IStreamChannelSender _result;
try {
_data.writeInterfaceToken(DESCRIPTOR);
if ((session!=null)) {
_data.writeInt(1);
session.writeToParcel(_data, 0);
}
else {
_data.writeInt(0);
}
_data.writeInt(channelId);
mRemote.transact(Stub.TRANSACTION_openSender, _data, _reply, 0);
_reply.readException();
_result = swaiotos.channel.iot.ss.channel.stream.IStreamChannelSender.Stub.asInterface(_reply.readStrongBinder());
}
finally {
_reply.recycle();
_data.recycle();
}
return _result;
}
@Override public void closeSender(swaiotos.channel.iot.ss.channel.stream.IStreamChannelSender sender) throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
try {
_data.writeInterfaceToken(DESCRIPTOR);
_data.writeStrongBinder((((sender!=null))?(sender.asBinder()):(null)));
mRemote.transact(Stub.TRANSACTION_closeSender, _data, _reply, 0);
_reply.readException();
}
finally {
_reply.recycle();
_data.recycle();
}
}
}
static final int TRANSACTION_open = (android.os.IBinder.FIRST_CALL_TRANSACTION + 0);
static final int TRANSACTION_close = (android.os.IBinder.FIRST_CALL_TRANSACTION + 1);
static final int TRANSACTION_openSender = (android.os.IBinder.FIRST_CALL_TRANSACTION + 2);
static final int TRANSACTION_closeSender = (android.os.IBinder.FIRST_CALL_TRANSACTION + 3);
}
public int open(swaiotos.channel.iot.ss.channel.stream.IStreamChannelReceiver stream) throws android.os.RemoteException;
public void close(int channelId) throws android.os.RemoteException;
public swaiotos.channel.iot.ss.channel.stream.IStreamChannelSender openSender(swaiotos.channel.iot.ss.session.Session session, int channelId) throws android.os.RemoteException;
public void closeSender(swaiotos.channel.iot.ss.channel.stream.IStreamChannelSender sender) throws android.os.RemoteException;
}
