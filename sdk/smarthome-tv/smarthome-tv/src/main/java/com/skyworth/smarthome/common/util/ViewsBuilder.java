package com.skyworth.smarthome.common.util;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.graphics.drawable.StateListDrawable;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.widget.CheckBox;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.ScrollView;
import android.widget.Switch;
import android.widget.TextView;

import com.skyworth.smarthome.common.model.AppData;
import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.ui.CommonFocusBox;
import com.skyworth.smarthome.common.ui.MarqueeText;
import com.skyworth.ui.api.widget.CCFocusDrawable;
import com.skyworth.ui.api.widget.SimpleFocusDrawable;
import com.skyworth.ui.newrecycleview.NewRecycleLayout;
import com.skyworth.util.Util;
import com.skyworth.util.imageloader.ImageLoader;
import com.smarthome.common.sal.SalImpl;
import com.smarthome.common.utils.XThemeUtils;
import com.swaiot.aiotlib.common.entity.DeviceBean;

import static com.skyworth.smarthome.SmartHomeTvLib.mContext;

/**
 * @ProjectName: NewTV_SmartHome
 * @Package: com.skyworth.smarthome_tv.utils
 * @ClassName: ViewsBuilder
 * @Description: java类作用描述
 * @Author: wangyuehui
 * @CreateDate: 2020/6/4 9:45
 * @UpdateUser: 更新者
 * @UpdateDate: 2020/6/4 9:45
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
public class ViewsBuilder {

    /**
     * 离线帮助页面 -wifi类设备
     */
    public static RelativeLayout getOfflineHelpOfWifi(final Context context) {

        RelativeLayout parent = new RelativeLayout(context, null, R.style.common_style);
        parent.setLayoutParams(new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT,
                RelativeLayout.LayoutParams.WRAP_CONTENT));

        RelativeLayout rootView = new RelativeLayout(context);
        RelativeLayout.LayoutParams rootViewRL = new RelativeLayout.LayoutParams(Util.Div(860), Util.Div(600));
        rootViewRL.addRule(RelativeLayout.CENTER_IN_PARENT);
        parent.addView(rootView, rootViewRL);
        rootView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
            }
        });

        TextView themeTextView = new TextView(context);
        themeTextView.setText(R.string.offline_help_theme);
        themeTextView.setTextColor(context.getResources().getColor(R.color.white));
        themeTextView.setTextSize(Util.Dpi(44));
        themeTextView.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
        themeTextView.setSingleLine(true);
        themeTextView.setGravity(Gravity.CENTER);
        RelativeLayout.LayoutParams themeRL = new RelativeLayout.LayoutParams(Util.Div(760), RelativeLayout.LayoutParams.WRAP_CONTENT);
        themeRL.topMargin = Util.Div(50);
        themeRL.addRule(RelativeLayout.CENTER_HORIZONTAL);
        rootView.addView(themeTextView, themeRL);

        TextView checkTextView = new TextView(context);
        checkTextView.setText(R.string.offline_help_check);
        checkTextView.setTextColor(context.getResources().getColor(R.color.white_60));
        checkTextView.setTextSize(Util.Dpi(32));
        checkTextView.setSingleLine(true);
        RelativeLayout.LayoutParams checkRL = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT, RelativeLayout.LayoutParams.WRAP_CONTENT);
        checkRL.topMargin = Util.Div(120);
        checkRL.addRule(RelativeLayout.CENTER_HORIZONTAL);
        rootView.addView(checkTextView, checkRL);


        TextView contentTextView = new TextView(context);
        contentTextView.setText(R.string.offline_help_content);
        contentTextView.setTextColor(context.getResources().getColor(R.color.white_60));
        contentTextView.setTextSize(Util.Dpi(32));
        contentTextView.setSingleLine(false);
        contentTextView.setLineSpacing(Util.Div(50), 0);
        RelativeLayout.LayoutParams contentRL = new RelativeLayout.LayoutParams(Util.Div(760), Util.Div(200));
        contentRL.topMargin = Util.Div(192);
        contentRL.addRule(RelativeLayout.CENTER_HORIZONTAL);
        rootView.addView(contentTextView, contentRL);

        CommonFocusBox rebindBtnLayout = new CommonFocusBox(context);
        rebindBtnLayout.setId(R.id.offline_help_rebind);
        RelativeLayout.LayoutParams rebindBtnLayoutRL = new RelativeLayout.LayoutParams(Util.Div(365 + 10), Util.Div(90 + 10));
        rebindBtnLayoutRL.topMargin = Util.Div(460 - 8);
        rebindBtnLayoutRL.leftMargin = Util.Div(50 - 8);
        rootView.addView(rebindBtnLayout, rebindBtnLayoutRL);

        final TextView rebingBtn = new TextView(context);
        rebingBtn.setText(R.string.offline_help_rebinding);
        rebingBtn.setTextColor(context.getResources().getColor(R.color.white_60));
        rebingBtn.setTextSize(Util.Dpi(32));
        rebingBtn.setSingleLine(true);
        rebingBtn.setGravity(Gravity.CENTER);
//        rebingBtn.setBackground(getNoFocused(context,Util.Div(16)));
        FrameLayout.LayoutParams rebingBtnRL = new FrameLayout.LayoutParams(Util.Div(365), Util.Div(90));
        rebingBtnRL.gravity = Gravity.CENTER;
        rebindBtnLayout.addView(rebingBtn, rebingBtnRL);

        rebindBtnLayout.setOnFocusChangeListener(new CommonFocusBox.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    rebingBtn.setTextColor(context.getResources().getColor(R.color.black));
//                    rebingBtn.setBackground(getFocused(context,Util.Div(16)));
                } else {
                    rebingBtn.setTextColor(context.getResources().getColor(R.color.white_60));
//                    rebingBtn.setBackground(getNoFocused(context,Util.Div(16)));
                }
            }
        });

        rebindBtnLayout.setOnTouchListener(new CommonFocusBox.OnTouchListener() {
            @Override
            public void onTouch(View v, MotionEvent event) {
                if (event.getAction() == MotionEvent.ACTION_DOWN) {
                    rebingBtn.setTextColor(context.getResources().getColor(R.color.black));
//                    rebingBtn.setBackground(getFocused(context,Util.Div(16)));
                } else if (event.getAction() == MotionEvent.ACTION_UP || event.getAction() == MotionEvent.ACTION_CANCEL) {
                    rebingBtn.setTextColor(context.getResources().getColor(R.color.white_60));
//                    rebingBtn.setBackground(getNoFocused(context,Util.Div(16)));
                }
            }
        });
        CommonFocusBox cancleBtnLayout = new CommonFocusBox(context);
        cancleBtnLayout.setId(R.id.offline_help_cancle);
        RelativeLayout.LayoutParams cancleBtnLayoutRL = new RelativeLayout.LayoutParams(Util.Div(365 + 10), Util.Div(90 + 10));
        cancleBtnLayoutRL.topMargin = Util.Div(460 - 8);
        cancleBtnLayoutRL.leftMargin = Util.Div(445 - 8);
        rootView.addView(cancleBtnLayout, cancleBtnLayoutRL);

        final TextView cancleBtn = new TextView(context);
        cancleBtn.setText(R.string.offline_help_cancle);
        cancleBtn.setTextColor(context.getResources().getColor(R.color.white_60));
        cancleBtn.setTextSize(Util.Dpi(32));
        cancleBtn.setSingleLine(true);
        cancleBtn.setGravity(Gravity.CENTER);
//        cancleBtn.setBackground(getNoFocused(context,Util.Div(16)));
        FrameLayout.LayoutParams rcancleBtnRL = new FrameLayout.LayoutParams(Util.Div(365), Util.Div(90));
        rcancleBtnRL.gravity = Gravity.CENTER;
        cancleBtnLayout.addView(cancleBtn, rcancleBtnRL);

        cancleBtnLayout.setOnFocusChangeListener(new CommonFocusBox.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    cancleBtn.setTextColor(context.getResources().getColor(R.color.black));
//                    cancleBtn.setBackground(getFocused(context,Util.Div(16)));
                } else {
                    cancleBtn.setTextColor(context.getResources().getColor(R.color.white_60));
//                    cancleBtn.setBackground(getNoFocused(context,Util.Div(16)));
                }
            }
        });

        cancleBtnLayout.setOnTouchListener(new CommonFocusBox.OnTouchListener() {
            @Override
            public void onTouch(View v, MotionEvent event) {
                if (event.getAction() == MotionEvent.ACTION_DOWN) {
                    cancleBtn.setTextColor(context.getResources().getColor(R.color.black));
//                    cancleBtn.setBackground(getFocused(context,Util.Div(16)));
                } else if (event.getAction() == MotionEvent.ACTION_UP || event.getAction() == MotionEvent.ACTION_CANCEL) {
                    cancleBtn.setTextColor(context.getResources().getColor(R.color.white_60));
//                    cancleBtn.setBackground(getNoFocused(context,Util.Div(16)));
                }
            }
        });

        GradientDrawable rootViewBackGroudDrawable = new GradientDrawable();
        rootViewBackGroudDrawable.setShape(GradientDrawable.RECTANGLE);
        rootViewBackGroudDrawable.setColor(context.getResources().getColor(R.color.color_444B53));
        rootViewBackGroudDrawable.setCornerRadius(Util.Div(16));
        rootView.setBackground(rootViewBackGroudDrawable);

        return parent;
    }


    /**
     * 离线帮助页面 -TV类(本机)设备
     */
    public static RelativeLayout getOfflineHelpOfTV(final Context context) {
        RelativeLayout parent = new RelativeLayout(context, null, R.style.common_style);
        parent.setLayoutParams(new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT,
                RelativeLayout.LayoutParams.WRAP_CONTENT));

        RelativeLayout rootView = new RelativeLayout(context);
        RelativeLayout.LayoutParams rootViewRL = new RelativeLayout.LayoutParams(Util.Div(860), Util.Div(800));
        rootViewRL.addRule(RelativeLayout.CENTER_IN_PARENT);
        parent.addView(rootView, rootViewRL);
        rootView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
            }
        });

        TextView themeTextView = new TextView(context);
        themeTextView.setText(R.string.offline_help_theme);
        themeTextView.setTextColor(context.getResources().getColor(R.color.white));
        themeTextView.setTextSize(Util.Dpi(44));
        themeTextView.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
        themeTextView.setSingleLine(true);
        themeTextView.setGravity(Gravity.CENTER);
        RelativeLayout.LayoutParams themeRL = new RelativeLayout.LayoutParams(Util.Div(760), RelativeLayout.LayoutParams.WRAP_CONTENT);
        themeRL.topMargin = Util.Div(50);
        themeRL.addRule(RelativeLayout.CENTER_HORIZONTAL);
        rootView.addView(themeTextView, themeRL);

        TextView checkTextView = new TextView(context);
        checkTextView.setText(R.string.offline_help_check);
        checkTextView.setTextColor(context.getResources().getColor(R.color.white_60));
        checkTextView.setTextSize(Util.Dpi(32));
        checkTextView.setSingleLine(true);
        RelativeLayout.LayoutParams checkRL = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT, RelativeLayout.LayoutParams.WRAP_CONTENT);
        checkRL.topMargin = Util.Div(120);
        checkRL.addRule(RelativeLayout.CENTER_HORIZONTAL);
        rootView.addView(checkTextView, checkRL);


        TextView contentTextView = new TextView(context);
        contentTextView.setText(R.string.local_machine_help_content);
        contentTextView.setTextColor(context.getResources().getColor(R.color.white_60));
        contentTextView.setTextSize(Util.Dpi(32));
        contentTextView.setSingleLine(false);
        contentTextView.setLineSpacing(Util.Div(50), 0);
        RelativeLayout.LayoutParams contentRL = new RelativeLayout.LayoutParams(Util.Div(760), Util.Div(390));
        contentRL.topMargin = Util.Div(192);
        contentRL.addRule(RelativeLayout.CENTER_HORIZONTAL);
        rootView.addView(contentTextView, contentRL);

        CommonFocusBox rebindBtnLayout = new CommonFocusBox(context);
        rebindBtnLayout.setId(R.id.offline_help_rebind);
        RelativeLayout.LayoutParams rebindBtnLayoutRL = new RelativeLayout.LayoutParams(Util.Div(365 + 10), Util.Div(90 + 10));
        rebindBtnLayoutRL.topMargin = Util.Div(660 - 8);
        rebindBtnLayoutRL.leftMargin = Util.Div(50 - 8);
        rootView.addView(rebindBtnLayout, rebindBtnLayoutRL);

        final ImageView imageLoading = new ImageView(context);
        imageLoading.setImageDrawable(context.getResources().getDrawable(R.drawable.tv_check_load));
        imageLoading.setId(R.id.offline_help_loading);
        FrameLayout.LayoutParams rebingBtnLoadingRL = new FrameLayout.LayoutParams(Util.Div(38), Util.Div(38));
        rebingBtnLoadingRL.gravity = Gravity.CENTER;
        rebingBtnLoadingRL.leftMargin = 150;
        rebingBtnLoadingRL.rightMargin = 20;
        rebingBtnLoadingRL.topMargin = 685;
        imageLoading.setVisibility(View.GONE);
        rootView.addView(imageLoading,rebingBtnLoadingRL);

        final TextView rebingBtn = new TextView(context);
        rebingBtn.setText(R.string.detect_help_rebinding);
        rebingBtn.setId(R.id.offline_help_check);
        rebingBtn.setTextColor(context.getResources().getColor(R.color.white_60));
        rebingBtn.setTextSize(Util.Dpi(32));
        rebingBtn.setSingleLine(true);
        rebingBtn.setGravity(Gravity.CENTER);
        FrameLayout.LayoutParams rebingBtnRL = new FrameLayout.LayoutParams(Util.Div(128), Util.Div(38));
        rebingBtnRL.gravity = Gravity.CENTER;
        rebingBtnRL.leftMargin = 10;
        rebindBtnLayout.addView(rebingBtn, rebingBtnRL);

        rebindBtnLayout.setOnFocusChangeListener(new CommonFocusBox.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    rebingBtn.setTextColor(context.getResources().getColor(R.color.black));
                } else {
                    rebingBtn.setTextColor(context.getResources().getColor(R.color.white_60));
                }
            }
        });

        rebindBtnLayout.setOnTouchListener(new CommonFocusBox.OnTouchListener() {
            @Override
            public void onTouch(View v, MotionEvent event) {
                if (event.getAction() == MotionEvent.ACTION_DOWN) {
                    rebingBtn.setTextColor(context.getResources().getColor(R.color.black));
                } else if (event.getAction() == MotionEvent.ACTION_UP || event.getAction() == MotionEvent.ACTION_CANCEL) {
                    rebingBtn.setTextColor(context.getResources().getColor(R.color.white_60));
                }
            }
        });

        CommonFocusBox cancleBtnLayout = new CommonFocusBox(context);
        cancleBtnLayout.setId(R.id.offline_help_cancle);
        RelativeLayout.LayoutParams cancleBtnLayoutRL = new RelativeLayout.LayoutParams(Util.Div(365 + 10), Util.Div(90 + 10));
        cancleBtnLayoutRL.topMargin = Util.Div(660 - 8);
        cancleBtnLayoutRL.leftMargin = Util.Div(445 - 8);
        rootView.addView(cancleBtnLayout, cancleBtnLayoutRL);

        final TextView cancleBtn = new TextView(context);
        cancleBtn.setText(R.string.offline_help_back);
        cancleBtn.setTextColor(context.getResources().getColor(R.color.white_60));
        cancleBtn.setTextSize(Util.Dpi(32));
        cancleBtn.setSingleLine(true);
        cancleBtn.setGravity(Gravity.CENTER);
        FrameLayout.LayoutParams rcancleBtnRL = new FrameLayout.LayoutParams(Util.Div(365), Util.Div(90));
        rcancleBtnRL.gravity = Gravity.CENTER;
        cancleBtnLayout.addView(cancleBtn, rcancleBtnRL);

        cancleBtnLayout.setOnFocusChangeListener(new CommonFocusBox.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    cancleBtn.setTextColor(context.getResources().getColor(R.color.black));
                } else {
                    cancleBtn.setTextColor(context.getResources().getColor(R.color.white_60));
                }
            }
        });

        cancleBtnLayout.setOnTouchListener(new CommonFocusBox.OnTouchListener() {
            @Override
            public void onTouch(View v, MotionEvent event) {
                if (event.getAction() == MotionEvent.ACTION_DOWN) {
                    cancleBtn.setTextColor(context.getResources().getColor(R.color.black));
                } else if (event.getAction() == MotionEvent.ACTION_UP || event.getAction() == MotionEvent.ACTION_CANCEL) {
                    cancleBtn.setTextColor(context.getResources().getColor(R.color.white_60));
                }
            }
        });

        GradientDrawable rootViewBackGroudDrawable = new GradientDrawable();
        rootViewBackGroudDrawable.setShape(GradientDrawable.RECTANGLE);
        rootViewBackGroudDrawable.setColor(context.getResources().getColor(R.color.color_444B53));
        rootViewBackGroudDrawable.setCornerRadius(Util.Div(16));
        rootView.setBackground(rootViewBackGroudDrawable);

        return parent;
    }
    /**
     * 离线帮助页面 -TV类(其他)设备
     */
    public static RelativeLayout getOfflineHelpOfTVOther(final Context context) {
        RelativeLayout parent = new RelativeLayout(context, null, R.style.common_style);
        parent.setLayoutParams(new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT,
                RelativeLayout.LayoutParams.WRAP_CONTENT));

        RelativeLayout rootView = new RelativeLayout(context);
        RelativeLayout.LayoutParams rootViewRL = new RelativeLayout.LayoutParams(Util.Div(860), Util.Div(800));
        rootViewRL.addRule(RelativeLayout.CENTER_IN_PARENT);
        parent.addView(rootView, rootViewRL);
        rootView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
            }
        });

        TextView themeTextView = new TextView(context);
        themeTextView.setText(R.string.offline_help_theme);
        themeTextView.setTextColor(context.getResources().getColor(R.color.white));
        themeTextView.setTextSize(Util.Dpi(44));
        themeTextView.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
        themeTextView.setSingleLine(true);
        themeTextView.setGravity(Gravity.CENTER);
        RelativeLayout.LayoutParams themeRL = new RelativeLayout.LayoutParams(Util.Div(760), RelativeLayout.LayoutParams.WRAP_CONTENT);
        themeRL.topMargin = Util.Div(50);
        themeRL.addRule(RelativeLayout.CENTER_HORIZONTAL);
        rootView.addView(themeTextView, themeRL);

        TextView checkTextView = new TextView(context);
        checkTextView.setText(R.string.offline_help_check);
        checkTextView.setTextColor(context.getResources().getColor(R.color.white_60));
        checkTextView.setTextSize(Util.Dpi(32));
        checkTextView.setSingleLine(true);
        RelativeLayout.LayoutParams checkRL = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT, RelativeLayout.LayoutParams.WRAP_CONTENT);
        checkRL.topMargin = Util.Div(120);
        checkRL.addRule(RelativeLayout.CENTER_HORIZONTAL);
        rootView.addView(checkTextView, checkRL);

        TextView contentTextView = new TextView(context);
        contentTextView.setText(R.string.other_television_help_content);
        contentTextView.setTextColor(context.getResources().getColor(R.color.white_60));
        contentTextView.setTextSize(Util.Dpi(32));
        contentTextView.setSingleLine(false);
        contentTextView.setLineSpacing(Util.Div(50), 0);
        RelativeLayout.LayoutParams contentRL = new RelativeLayout.LayoutParams(Util.Div(760), Util.Div(390));
        contentRL.topMargin = Util.Div(192);
        contentRL.addRule(RelativeLayout.CENTER_HORIZONTAL);
        rootView.addView(contentTextView, contentRL);

        CommonFocusBox cancleBtnLayout = new CommonFocusBox(context);
        cancleBtnLayout.setId(R.id.offline_help_cancle);
        RelativeLayout.LayoutParams cancleBtnLayoutRL = new RelativeLayout.LayoutParams(Util.Div(760 + 10), Util.Div(90 + 10));
        cancleBtnLayoutRL.topMargin = Util.Div(660 - 8);
        cancleBtnLayoutRL.leftMargin = Util.Div(50 - 8);
        rootView.addView(cancleBtnLayout, cancleBtnLayoutRL);

        final TextView cancleBtn = new TextView(context);
        cancleBtn.setText(R.string.know_help_rebinding);
        cancleBtn.setTextColor(context.getResources().getColor(R.color.white_60));
        cancleBtn.setTextSize(Util.Dpi(32));
        cancleBtn.setSingleLine(true);
        cancleBtn.setGravity(Gravity.CENTER);
        FrameLayout.LayoutParams rcancleBtnRL = new FrameLayout.LayoutParams(Util.Div(760), Util.Div(90));
        rcancleBtnRL.gravity = Gravity.CENTER;
        cancleBtnLayout.addView(cancleBtn, rcancleBtnRL);

        cancleBtnLayout.setOnFocusChangeListener(new CommonFocusBox.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    cancleBtn.setTextColor(context.getResources().getColor(R.color.black));
                } else {
                    cancleBtn.setTextColor(context.getResources().getColor(R.color.white_60));
                }
            }
        });

        cancleBtnLayout.setOnTouchListener(new CommonFocusBox.OnTouchListener() {
            @Override
            public void onTouch(View v, MotionEvent event) {
                if (event.getAction() == MotionEvent.ACTION_DOWN) {
                    cancleBtn.setTextColor(context.getResources().getColor(R.color.black));
                } else if (event.getAction() == MotionEvent.ACTION_UP || event.getAction() == MotionEvent.ACTION_CANCEL) {
                    cancleBtn.setTextColor(context.getResources().getColor(R.color.white_60));
                }
            }
        });

        GradientDrawable rootViewBackGroudDrawable = new GradientDrawable();
        rootViewBackGroudDrawable.setShape(GradientDrawable.RECTANGLE);
        rootViewBackGroudDrawable.setColor(context.getResources().getColor(R.color.color_444B53));
        rootViewBackGroudDrawable.setCornerRadius(Util.Div(16));
        rootView.setBackground(rootViewBackGroudDrawable);

        return parent;
    }
    /**
     * 离线帮助页面-zigbee类设备
     */
    public static RelativeLayout getOfflineHelpOfZigbee(final Context context) {

        RelativeLayout parent = new RelativeLayout(context, null, R.style.common_style);
        parent.setLayoutParams(new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT,
                RelativeLayout.LayoutParams.WRAP_CONTENT));

        RelativeLayout rootView = new RelativeLayout(context);
        RelativeLayout.LayoutParams rootViewRL = new RelativeLayout.LayoutParams(Util.Div(860), Util.Div(600));
        rootViewRL.addRule(RelativeLayout.CENTER_IN_PARENT);
        parent.addView(rootView, rootViewRL);
        rootView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
            }
        });

        TextView themeTextView = new TextView(context);
        themeTextView.setText(R.string.offline_help_theme);
        themeTextView.setTextColor(context.getResources().getColor(R.color.white));
        themeTextView.setTextSize(Util.Dpi(44));
        themeTextView.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
        themeTextView.setSingleLine(true);
        themeTextView.setGravity(Gravity.CENTER);
        RelativeLayout.LayoutParams themeRL = new RelativeLayout.LayoutParams(Util.Div(760), RelativeLayout.LayoutParams.WRAP_CONTENT);
        themeRL.topMargin = Util.Div(50);
        themeRL.addRule(RelativeLayout.CENTER_HORIZONTAL);
        rootView.addView(themeTextView, themeRL);

        TextView checkTextView = new TextView(context);
        checkTextView.setText(R.string.offline_help_check);
        checkTextView.setTextColor(context.getResources().getColor(R.color.white_60));
        checkTextView.setTextSize(Util.Dpi(32));
        checkTextView.setSingleLine(true);
        RelativeLayout.LayoutParams checkRL = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT, RelativeLayout.LayoutParams.WRAP_CONTENT);
        checkRL.topMargin = Util.Div(120);
        checkRL.addRule(RelativeLayout.CENTER_HORIZONTAL);
        rootView.addView(checkTextView, checkRL);


        TextView contentTextView = new TextView(context);
        contentTextView.setText(R.string.offline_help_content_2);
        contentTextView.setTextColor(context.getResources().getColor(R.color.white_60));
        contentTextView.setTextSize(Util.Dpi(32));
        contentTextView.setSingleLine(false);
        contentTextView.setLineSpacing(Util.Div(50), 0);
        contentTextView.setGravity(Gravity.CENTER_VERTICAL);
        RelativeLayout.LayoutParams contentRL = new RelativeLayout.LayoutParams(Util.Div(760), Util.Div(200));
        contentRL.topMargin = Util.Div(192);
        contentRL.addRule(RelativeLayout.CENTER_HORIZONTAL);
        rootView.addView(contentTextView, contentRL);

        CommonFocusBox sureBtnLayout = new CommonFocusBox(context);
        sureBtnLayout.setId(R.id.offline_help_sure);
        RelativeLayout.LayoutParams sureBtnLayoutRL = new RelativeLayout.LayoutParams(Util.Div(760 + 10), Util.Div(90 + 10));
        sureBtnLayoutRL.topMargin = Util.Div(460 - 8);
        sureBtnLayoutRL.addRule(RelativeLayout.CENTER_HORIZONTAL);
        rootView.addView(sureBtnLayout, sureBtnLayoutRL);

        final TextView sureBtn = new TextView(context);
        sureBtn.setText(R.string.offline_help_sure);
        sureBtn.setTextColor(context.getResources().getColor(R.color.white));
        sureBtn.setTextSize(Util.Dpi(32));
        sureBtn.setSingleLine(true);
        sureBtn.setGravity(Gravity.CENTER);
//        sureBtn.setBackground(getNoFocused(context,Util.Div(16)));

        sureBtnLayout.setOnFocusChangeListener(new CommonFocusBox.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    sureBtn.setTextColor(context.getResources().getColor(R.color.black));
//                    sureBtn.setBackground(getFocused(context,Util.Div(16)));
                } else {
                    sureBtn.setTextColor(context.getResources().getColor(R.color.white_60));
//                    sureBtn.setBackground(getNoFocused(context,Util.Div(16)));
                }
            }
        });

        sureBtnLayout.setOnTouchListener(new CommonFocusBox.OnTouchListener() {
            @Override
            public void onTouch(View v, MotionEvent event) {
                if (event.getAction() == MotionEvent.ACTION_DOWN) {
                    sureBtn.setTextColor(context.getResources().getColor(R.color.black));
//                    sureBtn.setBackground(getFocused(context,Util.Div(16)));
                } else if (event.getAction() == MotionEvent.ACTION_UP || event.getAction() == MotionEvent.ACTION_CANCEL) {
                    sureBtn.setTextColor(context.getResources().getColor(R.color.white_60));
//                    sureBtn.setBackground(getNoFocused(context,Util.Div(16)));
                }
            }
        });

        FrameLayout.LayoutParams sureBtnRL = new FrameLayout.LayoutParams(Util.Div(760), Util.Div(90));
        sureBtnRL.gravity = Gravity.CENTER;
        sureBtnLayout.addView(sureBtn, sureBtnRL);

        GradientDrawable rootViewBackGroudDrawable = new GradientDrawable();
        rootViewBackGroudDrawable.setShape(GradientDrawable.RECTANGLE);
        rootViewBackGroudDrawable.setColor(context.getResources().getColor(R.color.color_444B53));
        rootViewBackGroudDrawable.setCornerRadius(Util.Div(16));
        rootView.setBackground(rootViewBackGroudDrawable);

        return parent;
    }

    /**
     * 页面：添加设备指南
     */
    public static RelativeLayout getGuideAddDevices(final Context context) {

        RelativeLayout parent = new RelativeLayout(context, null, R.style.common_style);
        parent.setLayoutParams(new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT,
                RelativeLayout.LayoutParams.WRAP_CONTENT));

        RelativeLayout rootView = new RelativeLayout(context);
        if (SalImpl.getSAL(mContext).getDeviceChip().equals("9TA23")&&SalImpl.getSAL(mContext).getDeviceModel().equals("LB8")) {
            rootView.setBackground(context.getResources().getDrawable(R.drawable.guide_add_devices_background_black));
        } else {
            rootView.setBackground(context.getResources().getDrawable(R.drawable.guide_add_devices_background));
        }
        parent.addView(rootView, new RelativeLayout.LayoutParams(Util.Div(1920), Util.Div(1080)));

        getQrBoderCode(context, rootView);

        final RelativeLayout scheme2 = new RelativeLayout(context);
        scheme2.setBackground(context.getResources().getDrawable(R.drawable.guide_add_devices_scheme_2_no_focus));
        RelativeLayout.LayoutParams scheme2RL = new RelativeLayout.LayoutParams(Util.Div(410), Util.Div(285));
        scheme2RL.leftMargin = Util.Div(1450);
        scheme2RL.topMargin = Util.Div(356);
        rootView.addView(scheme2, scheme2RL);

        Drawable focusedDrawable = new CCFocusDrawable(context).setRadius(Util.Div(10)).setSolidVisible(false);

        StateListDrawable drawable = new StateListDrawable();
        //Non focused states
        drawable.addState(new int[]{-android.R.attr.state_focused, -android.R.attr.state_selected, -android.R.attr.state_pressed},
                context.getResources().getDrawable(R.color.translucent));
        drawable.addState(new int[]{-android.R.attr.state_focused, android.R.attr.state_selected, -android.R.attr.state_pressed},
                context.getResources().getDrawable(R.color.translucent));
        //Focused states
        drawable.addState(new int[]{android.R.attr.state_focused, -android.R.attr.state_selected, -android.R.attr.state_pressed},
                focusedDrawable);
        drawable.addState(new int[]{android.R.attr.state_focused, android.R.attr.state_selected, -android.R.attr.state_pressed},
                focusedDrawable);
        //Pressed
        drawable.addState(new int[]{android.R.attr.state_selected, android.R.attr.state_pressed},
                focusedDrawable);
        drawable.addState(new int[]{android.R.attr.state_pressed},
                focusedDrawable);

        RelativeLayout scheme2FocusLayout = new RelativeLayout(context);
        scheme2FocusLayout.setId(R.id.guide_add_devices_sheme_2);
        scheme2FocusLayout.setFocusable(true);
        scheme2FocusLayout.setClickable(true);
        scheme2FocusLayout.setBackground(drawable);
        RelativeLayout.LayoutParams scheme2FocusLayoutRL = new RelativeLayout.LayoutParams(Util.Div(380), Util.Div(257));
        scheme2FocusLayoutRL.leftMargin = Util.Div(15);
        scheme2FocusLayoutRL.topMargin = Util.Div(8);
        scheme2.addView(scheme2FocusLayout, scheme2FocusLayoutRL);

        scheme2FocusLayout.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    scheme2.setBackground(context.getResources().getDrawable(R.drawable.guide_add_devices_scheme_2_focus));
                } else {
                    scheme2.setBackground(context.getResources().getDrawable(R.drawable.guide_add_devices_scheme_2_no_focus));
                }
            }
        });


        final RelativeLayout scheme3 = new RelativeLayout(context);
        scheme3.setBackground(context.getResources().getDrawable(R.drawable.guide_add_devices_scheme_3_no_focus));

        RelativeLayout.LayoutParams scheme3RL = new RelativeLayout.LayoutParams(Util.Div(410), Util.Div(285));
        scheme3RL.leftMargin = Util.Div(1450);
        scheme3RL.topMargin = Util.Div(701);
        rootView.addView(scheme3, scheme3RL);

        StateListDrawable drawableScheme3 = new StateListDrawable();
        //Non focused states
        drawableScheme3.addState(new int[]{-android.R.attr.state_focused, -android.R.attr.state_selected, -android.R.attr.state_pressed},
                context.getResources().getDrawable(R.color.translucent));
        drawableScheme3.addState(new int[]{-android.R.attr.state_focused, android.R.attr.state_selected, -android.R.attr.state_pressed},
                context.getResources().getDrawable(R.color.translucent));
        //Focused states
        drawableScheme3.addState(new int[]{android.R.attr.state_focused, -android.R.attr.state_selected, -android.R.attr.state_pressed},
                focusedDrawable);
        drawableScheme3.addState(new int[]{android.R.attr.state_focused, android.R.attr.state_selected, -android.R.attr.state_pressed},
                focusedDrawable);
        //Pressed
        drawableScheme3.addState(new int[]{android.R.attr.state_selected, android.R.attr.state_pressed},
                focusedDrawable);
        drawableScheme3.addState(new int[]{android.R.attr.state_pressed},
                focusedDrawable);

        RelativeLayout scheme3FocusLayout = new RelativeLayout(context);
        scheme3FocusLayout.setFocusable(true);
        scheme3FocusLayout.setClickable(true);
        scheme3FocusLayout.setId(R.id.guide_add_devices_sheme_3);
        scheme3FocusLayout.setBackground(drawableScheme3);

        RelativeLayout.LayoutParams scheme3FocusLayoutRL = new RelativeLayout.LayoutParams(Util.Div(380), Util.Div(257));
        scheme3FocusLayoutRL.leftMargin = Util.Div(15);
        scheme3FocusLayoutRL.topMargin = Util.Div(8);
        scheme3.addView(scheme3FocusLayout, scheme3FocusLayoutRL);

        scheme3FocusLayout.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    scheme3.setBackground(context.getResources().getDrawable(R.drawable.guide_add_devices_scheme_3_focus));
                } else {
                    scheme3.setBackground(context.getResources().getDrawable(R.drawable.guide_add_devices_scheme_3_no_focus));
                }
            }
        });
        return parent;
    }

    private static void getQrBoderCode(Context context, RelativeLayout rootView) {
        GradientDrawable outerBorder = getGradientDrawable(Util.Div(16), Util.Div(2), context.getResources().getColor(R.color.white));

        GradientDrawable innerorderDrawable = new GradientDrawable();
        innerorderDrawable.setShape(GradientDrawable.RECTANGLE);
        innerorderDrawable.setCornerRadius(Util.Div(16));
        innerorderDrawable.setStroke(Util.Div(4), Color.parseColor("#5A687C"));
        innerorderDrawable.setColor(context.getResources().getColor(R.color.white));

        RelativeLayout qrLayout = new RelativeLayout(context);
        qrLayout.setPadding(Util.Div(2), Util.Div(2), Util.Div(2), Util.Div(2));
        qrLayout.setBackground(outerBorder);
        RelativeLayout.LayoutParams qrLayoutRL = new RelativeLayout.LayoutParams(Util.Div(360), Util.Div(360));
        qrLayoutRL.topMargin = Util.Div(560);
        qrLayoutRL.leftMargin = Util.Div(150);
        rootView.addView(qrLayout, qrLayoutRL);

        RelativeLayout innerLayout = new RelativeLayout(context);
        innerLayout.setPadding(Util.Div(10), Util.Div(10), Util.Div(10), Util.Div(10));
        innerLayout.setBackground(innerorderDrawable);
        RelativeLayout.LayoutParams innerRL = new RelativeLayout.LayoutParams(Util.Div(358), Util.Div(358));
        qrLayout.addView(innerLayout, innerRL);

        //二维码页面
        ImageView qrCodeImageView = new ImageView(context);
        RelativeLayout.LayoutParams qrCodeImageViewRL = new RelativeLayout.LayoutParams(Util.Div(350), Util.Div(350));
        innerLayout.addView(qrCodeImageView, qrCodeImageViewRL);

        try {
            final Bitmap bitmap = QRUtils.createQRImage(AppData.getInstance().getVHomeDownloadUrl(), Util.Div(350), Util.Div(350), null);
            if (bitmap != null) {
                qrCodeImageView.setImageBitmap(bitmap);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private static void getQrBoderSenceCode(Context context, RelativeLayout rootView) {
        GradientDrawable outerBorder = getGradientDrawable(Util.Div(16), Util.Div(2), context.getResources().getColor(R.color.white));

        GradientDrawable innerorderDrawable = new GradientDrawable();
        innerorderDrawable.setShape(GradientDrawable.RECTANGLE);
        innerorderDrawable.setCornerRadius(Util.Div(16));
        innerorderDrawable.setStroke(Util.Div(4), Color.parseColor("#5A687C"));
        innerorderDrawable.setColor(context.getResources().getColor(R.color.white));

        RelativeLayout qrLayout = new RelativeLayout(context);
        qrLayout.setPadding(Util.Div(2), Util.Div(2), Util.Div(2), Util.Div(2));
        qrLayout.setBackground(outerBorder);
        RelativeLayout.LayoutParams qrLayoutRL = new RelativeLayout.LayoutParams(Util.Div(440), Util.Div(440));
        qrLayoutRL.topMargin = Util.Div(478);
        qrLayoutRL.leftMargin = Util.Div(190);
        rootView.addView(qrLayout, qrLayoutRL);

        RelativeLayout innerLayout = new RelativeLayout(context);
        innerLayout.setPadding(Util.Div(10), Util.Div(10), Util.Div(10), Util.Div(10));
        innerLayout.setBackground(innerorderDrawable);
        RelativeLayout.LayoutParams innerRL = new RelativeLayout.LayoutParams(Util.Div(438), Util.Div(438));
        qrLayout.addView(innerLayout, innerRL);

        //二维码页面
        ImageView qrCodeImageView = new ImageView(context);
        RelativeLayout.LayoutParams qrCodeImageViewRL = new RelativeLayout.LayoutParams(Util.Div(430), Util.Div(430));
        innerLayout.addView(qrCodeImageView, qrCodeImageViewRL);

        try {
            final Bitmap bitmap = QRUtils.createQRImage(AppData.getInstance().getVHomeDownloadUrl(), Util.Div(430), Util.Div(430), null);
            if (bitmap != null) {
                qrCodeImageView.setImageBitmap(bitmap);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * @param redius 半径
     * @param stroke 空心大小
     * @param color  颜色
     */
    private static GradientDrawable getGradientDrawable(int redius, int stroke, int color) {
        GradientDrawable FocusedDrawable = new GradientDrawable();
        FocusedDrawable.setShape(GradientDrawable.RECTANGLE);
        FocusedDrawable.setCornerRadius(redius);
        FocusedDrawable.setStroke(stroke, color);
        return FocusedDrawable;
    }

    /**
     * 场景指南页面
     */
    public static RelativeLayout getGuideSence(final Context context) {

        RelativeLayout parent = new RelativeLayout(context, null, R.style.common_style);
        parent.setLayoutParams(new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT,
                RelativeLayout.LayoutParams.WRAP_CONTENT));

        RelativeLayout rootView = new RelativeLayout(context);
        if (SalImpl.getSAL(mContext).getDeviceChip().equals("9TA23")&&SalImpl.getSAL(mContext).getDeviceModel().equals("LB8")) {
            rootView.setBackground(context.getResources().getDrawable(R.drawable.guide_scene_black));
        } else {
            rootView.setBackground(context.getResources().getDrawable(R.drawable.guide_scene));
        }
        parent.addView(rootView, new RelativeLayout.LayoutParams(Util.Div(1920), Util.Div(1080)));

        getQrBoderSenceCode(context, rootView);


        return parent;
    }

    /**
     * 个人中心页面
     */
    public static RelativeLayout getPersonCenter(Context context) {
        RelativeLayout rootView = new RelativeLayout(context);
        rootView.setLayoutParams(new RelativeLayout.LayoutParams(Util.Div(1920), Util.Div(1080)));
//        rootView.setBackground(context.getResources().getDrawable(R.drawable.person_center_background));

        TextView centerLogoTextView = new TextView(context);
        centerLogoTextView.setText(R.string.person_center_logo);
        centerLogoTextView.setTextColor(context.getResources().getColor(R.color.white));
        centerLogoTextView.setTextSize(Util.Dpi(56));
        centerLogoTextView.setSingleLine(true);
        centerLogoTextView.setGravity(Gravity.CENTER);
        RelativeLayout.LayoutParams centerLogoRL = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT,
                RelativeLayout.LayoutParams.WRAP_CONTENT);
        centerLogoRL.topMargin = Util.Div(80);
        centerLogoRL.leftMargin = Util.Div(80);
        rootView.addView(centerLogoTextView, centerLogoRL);

        View coocaaDraweeView = ImageLoader.getLoader().getView(context);
        coocaaDraweeView.setBackgroundResource(R.drawable.ic_default_user);
        coocaaDraweeView.setId(R.id.person_center_family_logo);
        RelativeLayout.LayoutParams circleImageViewRL = new RelativeLayout.LayoutParams(Util.Div(120), Util.Div(120));
        circleImageViewRL.topMargin = Util.Div(200);
        circleImageViewRL.leftMargin = Util.Div(460);
        rootView.addView(coocaaDraweeView, circleImageViewRL);

        TextView familyTextView = new TextView(context);
        familyTextView.setId(R.id.person_center_family_name);
        familyTextView.setText(R.string.person_center_family_name);
        familyTextView.setTextColor(context.getResources().getColor(R.color.white));
        familyTextView.setTextSize(Util.Dpi(48));
        familyTextView.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
        familyTextView.setSingleLine(true);
        familyTextView.setGravity(Gravity.CENTER);
        RelativeLayout.LayoutParams familyRL = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT,
                RelativeLayout.LayoutParams.WRAP_CONTENT);
        familyRL.topMargin = Util.Div(212);
        familyRL.leftMargin = Util.Div(600);
        rootView.addView(familyTextView, familyRL);

        TextView devicesTextView = new TextView(context);
        devicesTextView.setId(R.id.person_center_family_devices);
        devicesTextView.setText(R.string.person_center_devices);
        devicesTextView.setTextColor(context.getResources().getColor(R.color.white_40));
        devicesTextView.setTextSize(Util.Dpi(28));
        devicesTextView.setSingleLine(true);
        RelativeLayout.LayoutParams devicesRL = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT,
                RelativeLayout.LayoutParams.WRAP_CONTENT);
        devicesRL.topMargin = Util.Div(280);
        devicesRL.leftMargin = Util.Div(600);
        rootView.addView(devicesTextView, devicesRL);

        TextView logoutTextView = new TextView(context);
        logoutTextView.setText(R.string.person_center_logout);
        logoutTextView.setTextColor(context.getResources().getColor(R.color.white));
        logoutTextView.setTextSize(Util.Dpi(28));
        logoutTextView.setSingleLine(true);
        logoutTextView.setGravity(Gravity.CENTER);
        logoutTextView.setFocusable(true);
        logoutTextView.setClickable(true);
        logoutTextView.setId(R.id.person_center_logout);
        logoutTextView.setBackground(getStateListDrawable(context, Util.Div(35)));
        RelativeLayout.LayoutParams logoutRL = new RelativeLayout.LayoutParams(Util.Div(200),
                Util.Div(70));
        logoutRL.topMargin = Util.Div(225);
        logoutRL.leftMargin = Util.Div(1260);
        rootView.addView(logoutTextView, logoutRL);

        ScrollView scrollView = new ScrollView(context);
        scrollView.setVerticalScrollBarEnabled(false);
        RelativeLayout.LayoutParams scrollViewRL = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT,
                RelativeLayout.LayoutParams.WRAP_CONTENT);
        scrollViewRL.topMargin = Util.Div(360);
        scrollViewRL.leftMargin = Util.Div(460);
        rootView.addView(scrollView, scrollViewRL);

        RelativeLayout contentLayout = new RelativeLayout(context);
//        contentLayout.setPadding(0,0,0,Util.Div(10));
        RelativeLayout.LayoutParams scontentLayoutRL = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT,
                RelativeLayout.LayoutParams.WRAP_CONTENT);
        scrollView.addView(contentLayout, scontentLayoutRL);

        //切换家庭
        RelativeLayout homeLayout = new RelativeLayout(context);
        homeLayout.setFocusable(true);
        homeLayout.setClickable(true);
        homeLayout.setBackground(getStateListDrawable(context, Util.Div(8)));
        homeLayout.setId(R.id.person_center_family);
        RelativeLayout.LayoutParams homeRL = new RelativeLayout.LayoutParams(Util.Div(1000), Util.Div(130));
        contentLayout.addView(homeLayout, homeRL);
        //关联账号
        RelativeLayout relateLayout = new RelativeLayout(context);
        relateLayout.setFocusable(true);
        relateLayout.setClickable(true);
        relateLayout.setBackground(getStateListDrawable(context, Util.Div(8)));
        relateLayout.setId(R.id.person_center_relate_account);
        RelativeLayout.LayoutParams relateRL = new RelativeLayout.LayoutParams(Util.Div(1000), Util.Div(130));
        relateRL.topMargin = Util.Div(140);
        contentLayout.addView(relateLayout, relateRL);

        //自动发现设备
        RelativeLayout automaticFoundDeviceLayout = new RelativeLayout(context);
        automaticFoundDeviceLayout.setFocusable(true);
        automaticFoundDeviceLayout.setClickable(true);
        automaticFoundDeviceLayout.setBackground(getStateListDrawable(context, Util.Div(8)));
        automaticFoundDeviceLayout.setId(R.id.person_center_find_device);
        RelativeLayout.LayoutParams automaticFoundDeviceLayoutRL = new RelativeLayout.LayoutParams(Util.Div(1000), Util.Div(130));
        automaticFoundDeviceLayoutRL.topMargin = Util.Div(280);
        contentLayout.addView(automaticFoundDeviceLayout, automaticFoundDeviceLayoutRL);

        //解绑设备
        RelativeLayout unbindLayout = new RelativeLayout(context);
        unbindLayout.setFocusable(true);
        unbindLayout.setClickable(true);
        unbindLayout.setBackground(getStateListDrawable(context, Util.Div(8)));
        unbindLayout.setId(R.id.person_center_unbind_device);
        RelativeLayout.LayoutParams unbindRL = new RelativeLayout.LayoutParams(Util.Div(1000), Util.Div(130));
        unbindRL.topMargin = Util.Div(420);
        contentLayout.addView(unbindLayout, unbindRL);

        //手机号
        RelativeLayout phoneLayout = new RelativeLayout(context);
        phoneLayout.setFocusable(true);
        phoneLayout.setClickable(true);
        phoneLayout.setBackground(getStateListDrawable(context, Util.Div(8)));
        phoneLayout.setId(R.id.person_center_phone);
        RelativeLayout.LayoutParams phoneRL = new RelativeLayout.LayoutParams(Util.Div(1000), Util.Div(130));
        phoneRL.topMargin = Util.Div(560);
        contentLayout.addView(phoneLayout, phoneRL);

        //版本信息
        RelativeLayout versionLayout = new RelativeLayout(context);
        versionLayout.setFocusable(true);
        versionLayout.setClickable(true);
        versionLayout.setBackground(getStateListDrawable(context, Util.Div(8)));
        versionLayout.setId(R.id.person_center_version);
        RelativeLayout.LayoutParams versionLayoutRL = new RelativeLayout.LayoutParams(Util.Div(1000), Util.Div(130));
        versionLayoutRL.topMargin = Util.Div(700);
        contentLayout.addView(versionLayout, versionLayoutRL);

        getPersonCentorBaseLayout(context, homeLayout, R.drawable.person_center_home, R.string.person_center_change_family, 1);
        getPersonCentorBaseLayout(context, relateLayout, R.drawable.person_center_relate_account, R.string.person_center_relate_account, 2);
        getPersonCentorBaseLayout(context, unbindLayout, R.drawable.person_center_unbind, R.string.person_center_unbind_device, 3);
        getPersonCentorBaseLayout(context, phoneLayout, R.drawable.person_center_phone, R.string.person_center_phone_number, 4);
        getPersonCentorBaseLayout(context, automaticFoundDeviceLayout, R.drawable.person_center_find_device, R.string.person_center_find_device, 5);
        getPersonCentorBaseLayout(context, versionLayout, R.drawable.person_center_version, R.string.person_center_version_info, 6);

        return rootView;
    }

    @SuppressLint("ClickableViewAccessibility")
    public static RelativeLayout getPersonCenter2(final Context context) {
        RelativeLayout rootView = new RelativeLayout(context);
        rootView.setLayoutParams(new RelativeLayout.LayoutParams(Util.Div(1920), Util.Div(1080)));
//        rootView.setBackground(context.getResources().getDrawable(R.drawable.person_center_background));

        TextView centerLogoTextView = new TextView(context);
        centerLogoTextView.setText(R.string.person_center_logo);
        centerLogoTextView.setTextColor(context.getResources().getColor(R.color.white));
        centerLogoTextView.setTextSize(Util.Dpi(56));
        centerLogoTextView.setSingleLine(true);
        centerLogoTextView.setGravity(Gravity.CENTER);
        centerLogoTextView.getPaint().setFakeBoldText(true);
        RelativeLayout.LayoutParams centerLogoRL = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT,
                RelativeLayout.LayoutParams.WRAP_CONTENT);
        centerLogoRL.topMargin = Util.Div(80);
        centerLogoRL.leftMargin = Util.Div(80);
        rootView.addView(centerLogoTextView, centerLogoRL);

        View coocaaDraweeView = ImageLoader.getLoader().getView(context);
        coocaaDraweeView.setBackgroundResource(R.drawable.ic_default_user);
        coocaaDraweeView.setId(R.id.person_center_family_logo);
        RelativeLayout.LayoutParams circleImageViewRL = new RelativeLayout.LayoutParams(Util.Div(120), Util.Div(120));
        circleImageViewRL.topMargin = Util.Div(200);
        circleImageViewRL.leftMargin = Util.Div(460);
        rootView.addView(coocaaDraweeView, circleImageViewRL);

        TextView familyTextView = new TextView(context);
        familyTextView.setId(R.id.person_center_family_name);
        familyTextView.setText("");
        familyTextView.setTextColor(context.getResources().getColor(R.color.white));
        familyTextView.setTextSize(Util.Dpi(48));
        familyTextView.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
        familyTextView.setSingleLine(true);
        familyTextView.setGravity(Gravity.CENTER);
        RelativeLayout.LayoutParams familyRL = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT,
                RelativeLayout.LayoutParams.WRAP_CONTENT);
        familyRL.topMargin = Util.Div(212);
        familyRL.leftMargin = Util.Div(600);
        rootView.addView(familyTextView, familyRL);

        TextView devicesTextView = new TextView(context);
        devicesTextView.setId(R.id.person_center_family_devices);
        devicesTextView.setText("");
        devicesTextView.setTextColor(context.getResources().getColor(R.color.white_40));
        devicesTextView.setTextSize(Util.Dpi(28));
        devicesTextView.setSingleLine(true);
        RelativeLayout.LayoutParams devicesRL = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT,
                RelativeLayout.LayoutParams.WRAP_CONTENT);
        devicesRL.topMargin = Util.Div(280);
        devicesRL.leftMargin = Util.Div(600);
        rootView.addView(devicesTextView, devicesRL);

        final RelativeLayout outsideLayout = new RelativeLayout(context);
        outsideLayout.setPadding(Util.Div(8), Util.Div(8), Util.Div(8), Util.Div(8));
        RelativeLayout.LayoutParams RL = new RelativeLayout.LayoutParams(Util.Div(192 + 16), Util.Div(86));
        RL.topMargin = Util.Div(225 - 8);
        RL.leftMargin = Util.Div(1260);
        rootView.addView(outsideLayout, RL);

        final SimpleFocusDrawable logoutTextViewBg = new SimpleFocusDrawable(context).setRadius(Util.Div(35));
        final TextView logoutTextView = new TextView(context);
        logoutTextView.setText(R.string.person_center_logout);
        logoutTextView.setTextColor(context.getResources().getColor(R.color.white_80));
        logoutTextView.setTextSize(Util.Dpi(28));
        logoutTextView.setSingleLine(true);
        logoutTextView.setGravity(Gravity.CENTER);
        logoutTextView.setFocusable(true);
        logoutTextView.setFocusableInTouchMode(true);
        logoutTextView.setClickable(true);
        logoutTextView.setId(R.id.person_center_logout);
        logoutTextView.setBackground(logoutTextViewBg);
        RelativeLayout.LayoutParams logoutRL = new RelativeLayout.LayoutParams(Util.Div(192),
                Util.Div(70));
        outsideLayout.addView(logoutTextView, logoutRL);

        logoutTextView.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                logoutTextViewBg.setFocus(hasFocus);
                logoutTextView.setTextColor(context.getResources().getColor(hasFocus ? R.color.black : R.color.white_80));
            }
        });

        logoutTextView.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                if (event.getAction() == MotionEvent.ACTION_DOWN) {
                    if (v.isFocusable() && v.isFocusableInTouchMode()) {
                        v.requestFocus();
                    }
                } else if (event.getAction() == MotionEvent.ACTION_UP) {
                    return v.callOnClick();
                }
//                if (event.getAction() == MotionEvent.ACTION_DOWN) {
//                    logoutTextViewBg.setFocus(true);
//                    logoutTextView.setTextColor(context.getResources().getColor(R.color.black));
//                } else if (event.getAction() == MotionEvent.ACTION_UP || event.getAction() == MotionEvent.ACTION_CANCEL) {
//                    logoutTextViewBg.setFocus(false);
//                    logoutTextView.setTextColor(context.getResources().getColor(R.color.white_80));
//                }
                return false;
            }
        });

        final ScrollView scrollView = new ScrollView(context);
        scrollView.setVerticalScrollBarEnabled(false);
        RelativeLayout.LayoutParams scrollViewRL = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT,
                RelativeLayout.LayoutParams.WRAP_CONTENT);
        scrollViewRL.topMargin = Util.Div(352);
        scrollViewRL.leftMargin = Util.Div(452);
        rootView.addView(scrollView, scrollViewRL);


        LinearLayout contentLayout = new LinearLayout(context);
//        contentLayout.setPadding(0,0,0,Util.Div(10));
        RelativeLayout.LayoutParams scontentLayoutRL = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT,
                RelativeLayout.LayoutParams.WRAP_CONTENT);
        contentLayout.setOrientation(LinearLayout.VERTICAL);
        scrollView.addView(contentLayout, scontentLayoutRL);

        RelativeLayout netOutsideLayout = getOutSideRelativeLayout(context, contentLayout, -1);
        //网络开关
        RelativeLayout netLayout = new RelativeLayout(context);
        netLayout.setId(R.id.person_center_net_switch);
        RelativeLayout.LayoutParams netLayoutRL = new RelativeLayout.LayoutParams(Util.Div(1000), Util.Div(130));
        netOutsideLayout.addView(netLayout, netLayoutRL);


        RelativeLayout enableServiceOutsideLayout = getOutSideRelativeLayout(context, contentLayout, 0);
        //启用服务
        RelativeLayout enableServiceLayout = new RelativeLayout(context);
        enableServiceLayout.setId(R.id.person_center_enable_service);
        RelativeLayout.LayoutParams enableServiceRL = new RelativeLayout.LayoutParams(Util.Div(1000), Util.Div(130));
        enableServiceOutsideLayout.addView(enableServiceLayout, enableServiceRL);

        RelativeLayout homeOutsideLayout = getOutSideRelativeLayout(context, contentLayout, 1);
        //切换家庭
        RelativeLayout homeLayout = new RelativeLayout(context);
        homeLayout.setId(R.id.person_center_family);
        RelativeLayout.LayoutParams homeRL = new RelativeLayout.LayoutParams(Util.Div(1000), Util.Div(130));
        homeOutsideLayout.addView(homeLayout, homeRL);

        RelativeLayout relateOutsideLayout = getOutSideRelativeLayout(context, contentLayout, 2);
        //关联账号
        RelativeLayout relateLayout = new RelativeLayout(context);
        relateLayout.setId(R.id.person_center_relate_account);
        RelativeLayout.LayoutParams relateRL = new RelativeLayout.LayoutParams(Util.Div(1000), Util.Div(130));
        relateOutsideLayout.addView(relateLayout, relateRL);

        RelativeLayout automaticFoundDeviceOutsideLayout = getOutSideRelativeLayout(context, contentLayout, 3);
        //自动发现设备
        RelativeLayout automaticFoundDeviceLayout = new RelativeLayout(context);
        automaticFoundDeviceLayout.setId(R.id.person_center_find_device);
        RelativeLayout.LayoutParams automaticFoundDeviceLayoutRL = new RelativeLayout.LayoutParams(Util.Div(1000), Util.Div(130));
        automaticFoundDeviceOutsideLayout.addView(automaticFoundDeviceLayout, automaticFoundDeviceLayoutRL);

        RelativeLayout unbindOutsideLayout = getOutSideRelativeLayout(context, contentLayout, 4);
        //解绑设备
        RelativeLayout unbindLayout = new RelativeLayout(context);
        unbindLayout.setId(R.id.person_center_unbind_device);
        RelativeLayout.LayoutParams unbindRL = new RelativeLayout.LayoutParams(Util.Div(1000), Util.Div(130));
        unbindOutsideLayout.addView(unbindLayout, unbindRL);

//        RelativeLayout orderOutsideLayout = getOutSideRelativeLayout(context, contentLayout,4);
//        //订单
//        RelativeLayout orderLayout = new RelativeLayout(context);
////        orderLayout.setFocusable(true);
////        orderLayout.setClickable(true);
////        orderLayout.setId(R.id.person_center_order);
//        orderLayout.setBackground(getNoFocused(context,Util.Div(8)));
//        RelativeLayout.LayoutParams orderLayoutRL = new RelativeLayout.LayoutParams(Util.Div(1000),Util.Div(130));
//        orderOutsideLayout.addView(orderLayout,orderLayoutRL);

        RelativeLayout phoneOutsideLayout = getOutSideRelativeLayout(context, contentLayout, 5);
        //手机号
        RelativeLayout phoneLayout = new RelativeLayout(context);
        phoneLayout.setId(R.id.person_center_phone);
        RelativeLayout.LayoutParams phoneRL = new RelativeLayout.LayoutParams(Util.Div(1000), Util.Div(130));
        phoneOutsideLayout.addView(phoneLayout, phoneRL);

        RelativeLayout versionOutsideLayout = getOutSideRelativeLayout(context, contentLayout, 6);
        //版本信息
        RelativeLayout versionLayout = new RelativeLayout(context);
        versionLayout.setId(R.id.person_center_version);
        RelativeLayout.LayoutParams versionLayoutRL = new RelativeLayout.LayoutParams(Util.Div(1000), Util.Div(130));
        versionOutsideLayout.addView(versionLayout, versionLayoutRL);

        getPersonCentorBaseLayout2(context, enableServiceOutsideLayout, enableServiceLayout, R.drawable.person_center_version, R.drawable.person_center_version_black, R.string.person_center_enable_service, 8);
        getPersonCentorBaseLayout2(context, homeOutsideLayout, homeLayout, R.drawable.person_center_home, R.drawable.person_center_home_black, R.string.person_center_change_family, 1);
        getPersonCentorBaseLayout2(context, relateOutsideLayout, relateLayout, R.drawable.person_center_relate_account, R.drawable.person_center_relate_account_black, R.string.person_center_relate_account, 2);
        getPersonCentorBaseLayout2(context, unbindOutsideLayout, unbindLayout, R.drawable.person_center_unbind, R.drawable.person_center_unbind_black, R.string.person_center_unbind_device, 3);
        getPersonCentorBaseLayout2(context, phoneOutsideLayout, phoneLayout, R.drawable.person_center_phone, R.drawable.person_center_phone_black, R.string.person_center_phone_number, 4);
        getPersonCentorBaseLayout2(context, automaticFoundDeviceOutsideLayout, automaticFoundDeviceLayout, R.drawable.person_center_find_device, R.drawable.person_center_find_device_black, R.string.person_center_find_device, 5);
        getPersonCentorBaseLayout2(context, versionOutsideLayout, versionLayout, R.drawable.person_center_version, R.drawable.person_center_version_black, R.string.person_center_version_info, 6);
        getPersonCentorBaseLayout2(context, netOutsideLayout, netLayout, R.drawable.person_center_version, R.drawable.person_center_version_black, R.string.person_center_net_switch, 0);
//        getPersonCentorBaseLayout2(context,orderOutsideLayout ,orderLayout,R.drawable.person_center_order,R.drawable.person_center_order_black,R.string.person_center_order,7);

        return rootView;
    }

    private static RelativeLayout getOutSideRelativeLayout(Context context, LinearLayout contentLayout, int i) {
        RelativeLayout outsideLayout = new RelativeLayout(context);
        outsideLayout.setFocusable(true);
        outsideLayout.setFocusableInTouchMode(true);
        outsideLayout.setPressed(true);
        outsideLayout.setClickable(true);
        outsideLayout.setGravity(Gravity.CENTER_VERTICAL);
        if (i == 0) {
            outsideLayout.setId(R.id.person_center_family);
        } else if (i == 1) {
            outsideLayout.setId(R.id.person_center_relate_account);
        } else if (i == 2) {
            outsideLayout.setId(R.id.person_center_find_device);
        } else if (i == 3) {
            outsideLayout.setId(R.id.person_center_unbind_device);
        } else if (i == 4) {
//            outsideLayout.setId(R.id.person_center_order);
            outsideLayout.setId(R.id.person_center_phone);
        } else if (i == 5) {
//            outsideLayout.setId(R.id.person_center_phone);
            outsideLayout.setId(R.id.person_center_version);
        } else if (i == 6) {
//            outsideLayout.setId(R.id.person_center_version);
        } else if (i == -1) {
            outsideLayout.setId(R.id.person_center_net_switch);
        }
//        outsideLayout.setPadding(Util.Div(8),Util.Div(16),Util.Div(8),Util.Div(16));
        RelativeLayout.LayoutParams RL = new RelativeLayout.LayoutParams(Util.Div(1010), Util.Div(140));
        contentLayout.addView(outsideLayout, RL);
        return outsideLayout;
    }

    /**
     * @param type 1:home  2:relate 3:unbind 4:phone
     */
    private static void getPersonCentorBaseLayout(final Context context, RelativeLayout layout, int logoRes, int themeTips, int type) {
        //logo图片
        final ImageView imageView = new ImageView(context);
        imageView.setImageDrawable(context.getResources().getDrawable(logoRes));
        imageView.setAlpha(0.8f);
        RelativeLayout.LayoutParams imageViewRL = new RelativeLayout.LayoutParams(Util.Div(50), Util.Div(50));
        imageViewRL.leftMargin = Util.Div(40);
        imageViewRL.addRule(RelativeLayout.CENTER_VERTICAL);
        layout.addView(imageView, imageViewRL);
        //theme主题
        final TextView textView = new TextView(context);
        textView.setText(themeTips);
        textView.setTextColor(context.getResources().getColor(R.color.white_80));
        textView.setTextSize(Util.Dpi(32));
        textView.setSingleLine(true);

        RelativeLayout.LayoutParams textViewRL = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT,
                RelativeLayout.LayoutParams.WRAP_CONTENT);
        textViewRL.leftMargin = Util.Div(110);
        textViewRL.addRule(RelativeLayout.CENTER_VERTICAL);
        layout.addView(textView, textViewRL);

        final ImageView imageViewLeft = new ImageView(context);
        imageViewLeft.setAlpha(0.8f);
        if (type == 1 || type == 5) {
            imageViewLeft.setImageDrawable(context.getResources().getDrawable(R.drawable.left));
            RelativeLayout.LayoutParams imageViewLeftRL = new RelativeLayout.LayoutParams(Util.Div(12), Util.Div(20));
            imageViewLeftRL.leftMargin = Util.Div(777);
            imageViewLeftRL.addRule(RelativeLayout.CENTER_VERTICAL);
            layout.addView(imageViewLeft, imageViewLeftRL);
        }

        final ImageView imageViewRight = new ImageView(context);
        if (type == 4)
            imageViewRight.setId(R.id.person_center_right);

        imageViewRight.setImageDrawable(context.getResources().getDrawable(R.drawable.right));
        imageViewRight.setAlpha(0.8f);
        RelativeLayout.LayoutParams imageViewRightRL = new RelativeLayout.LayoutParams(Util.Div(12), Util.Div(20));
        imageViewRightRL.leftMargin = Util.Div(948);
        imageViewRightRL.addRule(RelativeLayout.CENTER_VERTICAL);
        layout.addView(imageViewRight, imageViewRightRL);

        final TextView tipsTextView = new TextView(context);
        tipsTextView.setTextColor(context.getResources().getColor(R.color.white_80));
        tipsTextView.setTextSize(Util.Dpi(28));
        tipsTextView.setSingleLine(true);
        if (type == 1) {
            tipsTextView.setId(R.id.person_center_switch_family_name);
            tipsTextView.setText(R.string.person_center_family_name);
            tipsTextView.setGravity(Gravity.CENTER);
            tipsTextView.setMarqueeRepeatLimit(-1);
            RelativeLayout.LayoutParams tipsRL = new RelativeLayout.LayoutParams(Util.Div(150),
                    RelativeLayout.LayoutParams.WRAP_CONTENT);
            tipsRL.leftMargin = Util.Div(796);
            tipsRL.addRule(RelativeLayout.CENTER_VERTICAL);
            layout.addView(tipsTextView, tipsRL);
        } else if (type == 4) {
            tipsTextView.setId(R.id.person_center_phone_number);
            tipsTextView.setText(R.string.person_center_bind_device);
            tipsTextView.setGravity(Gravity.END);
            RelativeLayout.LayoutParams tipsRL = new RelativeLayout.LayoutParams(Util.Div(150),
                    RelativeLayout.LayoutParams.WRAP_CONTENT);
            tipsRL.rightMargin = Util.Div(72);
            tipsRL.addRule(RelativeLayout.CENTER_VERTICAL);
            tipsRL.addRule(RelativeLayout.ALIGN_PARENT_RIGHT);
            layout.addView(tipsTextView, tipsRL);
        } else if (type == 5) {
            tipsTextView.setId(R.id.person_center_find_device_tips);
            tipsTextView.setText(R.string.person_center_find_device_close);
            tipsTextView.setGravity(Gravity.CENTER);
            RelativeLayout.LayoutParams tipsRL = new RelativeLayout.LayoutParams(Util.Div(150),
                    RelativeLayout.LayoutParams.WRAP_CONTENT);
            tipsRL.leftMargin = Util.Div(796);
            tipsRL.addRule(RelativeLayout.CENTER_VERTICAL);
            layout.addView(tipsTextView, tipsRL);
        } else if (type == 6) {
            String pkName = context.getPackageName();
            try {
                String versionName = context.getPackageManager().getPackageInfo(
                        pkName, 0).versionName;
                tipsTextView.setText(String.format(context.getResources().getString(R.string.person_center_V), versionName));

                RelativeLayout.LayoutParams tipsRL = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT,
                        RelativeLayout.LayoutParams.WRAP_CONTENT);
                tipsRL.rightMargin = Util.Div(40);
                tipsRL.addRule(RelativeLayout.CENTER_VERTICAL);
                tipsRL.addRule(RelativeLayout.ALIGN_PARENT_RIGHT);
                layout.addView(tipsTextView, tipsRL);
            } catch (PackageManager.NameNotFoundException e) {
                e.printStackTrace();
            }
            imageViewRight.setVisibility(View.GONE);
        } else if (type == 8) {
            //启用服务开关
            imageViewRight.setVisibility(View.GONE);
            Switch serviceSwitch = new Switch(context);
            serviceSwitch.setId(R.id.person_center_enable_service_switch);
            serviceSwitch.setFocusable(false);
            serviceSwitch.setFocusableInTouchMode(false);
            serviceSwitch.setThumbResource(R.drawable.selector_switch_thumb);
            serviceSwitch.setTrackResource(R.drawable.selector_switch_track);
            // 从缓存中读取启用服务状态，默认为false（不启用）
            boolean enableServiceStatus = com.skyworth.smarthome.common.util.DataCacheUtil.getInstance().getBoolean(com.skyworth.smarthome.common.util.DataCacheUtil.KEY_ENABLE_SERVICE_STATUS, false);
            serviceSwitch.setChecked(enableServiceStatus);
            RelativeLayout.LayoutParams lp_service_switch = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT,
                    RelativeLayout.LayoutParams.WRAP_CONTENT);
            lp_service_switch.leftMargin = Util.Div(900);
            lp_service_switch.addRule(RelativeLayout.CENTER_VERTICAL);
            layout.addView(serviceSwitch, lp_service_switch);
        } else {
            tipsTextView.setVisibility(View.GONE);
        }

        layout.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    imageView.setAlpha(1.0f);
                    imageViewRight.setAlpha(1.0f);
                    imageViewLeft.setAlpha(1.0f);
                    textView.setTextColor(context.getResources().getColor(R.color.white));
                    tipsTextView.setTextColor(context.getResources().getColor(R.color.white));
                } else {
                    imageView.setAlpha(0.8f);
                    imageViewRight.setAlpha(0.8f);
                    imageViewLeft.setAlpha(0.8f);
                    textView.setTextColor(context.getResources().getColor(R.color.white_80));
                    tipsTextView.setTextColor(context.getResources().getColor(R.color.white_80));
                }
            }
        });


    }

    /**
     * @param type 1:home  2:relate 3:unbind 4:phone 5:find 6:version 7:order
     */
    @SuppressLint("ClickableViewAccessibility")
    private static void getPersonCentorBaseLayout2(final Context context, final RelativeLayout parent,
                                                   final RelativeLayout layout, final int logoRes, final int logRes_black, int themeTips, int type) {
        final SimpleFocusDrawable layoutFocusBg = new SimpleFocusDrawable(context).setRadius(Util.Div(8));
        parent.setBackground(layoutFocusBg);
        //logo图片
        final ImageView imageView = new ImageView(context);
        imageView.setImageDrawable(context.getResources().getDrawable(logoRes));
        RelativeLayout.LayoutParams imageViewRL = new RelativeLayout.LayoutParams(Util.Div(50), Util.Div(50));
        imageViewRL.leftMargin = Util.Div(40);
        imageViewRL.addRule(RelativeLayout.CENTER_VERTICAL);
        layout.addView(imageView, imageViewRL);
        //theme主题
        final TextView textView = new TextView(context);
        textView.setText(themeTips);
        textView.setTextColor(context.getResources().getColor(R.color.white));
        textView.setTextSize(Util.Dpi(32));
        textView.setSingleLine(true);

        RelativeLayout.LayoutParams textViewRL = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT,
                RelativeLayout.LayoutParams.WRAP_CONTENT);
        textViewRL.leftMargin = Util.Div(110);
        textViewRL.addRule(RelativeLayout.CENTER_VERTICAL);
        layout.addView(textView, textViewRL);

        final ImageView imageViewLeft = new ImageView(context);
        if (type == 1 || type == 5) {
            imageViewLeft.setImageDrawable(context.getResources().getDrawable(R.drawable.left));
            RelativeLayout.LayoutParams imageViewLeftRL = new RelativeLayout.LayoutParams(Util.Div(12), Util.Div(20));
            imageViewLeftRL.leftMargin = Util.Div(777);
            imageViewLeftRL.addRule(RelativeLayout.CENTER_VERTICAL);
            layout.addView(imageViewLeft, imageViewLeftRL);
        }

        final ImageView imageViewRight = new ImageView(context);
        if (type == 4)
            imageViewRight.setId(R.id.person_center_right);

        imageViewRight.setImageDrawable(context.getResources().getDrawable(R.drawable.right));
        RelativeLayout.LayoutParams imageViewRightRL = new RelativeLayout.LayoutParams(Util.Div(12), Util.Div(20));
        imageViewRightRL.leftMargin = Util.Div(948);
        imageViewRightRL.addRule(RelativeLayout.CENTER_VERTICAL);
        layout.addView(imageViewRight, imageViewRightRL);

        final MarqueeText tipsTextView = new MarqueeText(context);
        tipsTextView.setTextColor(context.getResources().getColor(R.color.white_80));
        tipsTextView.setTextSize(Util.Dpi(28));
        tipsTextView.setSingleLine(true);
        if (type == 1) {
            tipsTextView.setId(R.id.person_center_switch_family_name);
            tipsTextView.setText("");
            tipsTextView.setGravity(Gravity.CENTER);
            tipsTextView.setEllipsize(TextUtils.TruncateAt.MARQUEE);
            tipsTextView.setSingleLine(true);
            tipsTextView.setMarqueeRepeatLimit(-1);
            RelativeLayout.LayoutParams tipsRL = new RelativeLayout.LayoutParams(Util.Div(150),
                    RelativeLayout.LayoutParams.WRAP_CONTENT);
            tipsRL.leftMargin = Util.Div(796);
            tipsRL.addRule(RelativeLayout.CENTER_VERTICAL);
            layout.addView(tipsTextView, tipsRL);
        } else if (type == 4) {
            tipsTextView.setId(R.id.person_center_phone_number);
            tipsTextView.setText(R.string.person_center_bind_device);
            tipsTextView.setGravity(Gravity.END);
            RelativeLayout.LayoutParams tipsRL = new RelativeLayout.LayoutParams(Util.Div(150),
                    RelativeLayout.LayoutParams.WRAP_CONTENT);
            tipsRL.rightMargin = Util.Div(72);
            tipsRL.addRule(RelativeLayout.CENTER_VERTICAL);
            tipsRL.addRule(RelativeLayout.ALIGN_PARENT_RIGHT);
            layout.addView(tipsTextView, tipsRL);
        } else if (type == 5) {
            tipsTextView.setId(R.id.person_center_find_device_tips);
            tipsTextView.setText(R.string.person_center_find_device_close);
            tipsTextView.setGravity(Gravity.CENTER);
            RelativeLayout.LayoutParams tipsRL = new RelativeLayout.LayoutParams(Util.Div(150),
                    RelativeLayout.LayoutParams.WRAP_CONTENT);
            tipsRL.leftMargin = Util.Div(796);
            tipsRL.addRule(RelativeLayout.CENTER_VERTICAL);
            layout.addView(tipsTextView, tipsRL);
        } else if (type == 6) {
            String pkName = context.getPackageName();
            try {
                String versionName = context.getPackageManager().getPackageInfo(
                        pkName, 0).versionName;
                tipsTextView.setText(String.format(context.getResources().getString(R.string.person_center_V), versionName));

                RelativeLayout.LayoutParams tipsRL = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT,
                        RelativeLayout.LayoutParams.WRAP_CONTENT);
                tipsRL.rightMargin = Util.Div(40);
                tipsRL.addRule(RelativeLayout.CENTER_VERTICAL);
                tipsRL.addRule(RelativeLayout.ALIGN_PARENT_RIGHT);
                layout.addView(tipsTextView, tipsRL);
            } catch (PackageManager.NameNotFoundException e) {
                e.printStackTrace();
            }
            imageViewRight.setVisibility(View.GONE);
        } else if (type == 0) {
            //网络开关
            imageViewRight.setVisibility(View.GONE);
            Switch aSwitch = new Switch(context);
            aSwitch.setFocusable(false);
            aSwitch.setFocusableInTouchMode(false);
            aSwitch.setThumbResource(R.drawable.selector_switch_thumb);
            aSwitch.setTrackResource(R.drawable.selector_switch_track);
            try {
                int netSwitch = Settings.System.getInt(context.getContentResolver(), "netSwitch");
                aSwitch.setChecked(netSwitch == 2);
            } catch (Settings.SettingNotFoundException e) {
                e.printStackTrace();
            }
            RelativeLayout.LayoutParams lp_net_switch = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT,
                    RelativeLayout.LayoutParams.WRAP_CONTENT);
            lp_net_switch.leftMargin = Util.Div(900);
            lp_net_switch.addRule(RelativeLayout.CENTER_VERTICAL);
            layout.addView(aSwitch, lp_net_switch);
        } else {
            tipsTextView.setVisibility(View.GONE);
        }

        parent.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                layoutFocusBg.setFocus(hasFocus);
                if (hasFocus) {
                    imageView.setImageDrawable(context.getResources().getDrawable(logRes_black));
                    textView.setTextColor(context.getResources().getColor(R.color.black));
                    imageViewLeft.setImageDrawable(context.getResources().getDrawable(R.drawable.left_black));
                    imageViewRight.setImageDrawable(context.getResources().getDrawable(R.drawable.right_black));
                    tipsTextView.setTextColor(context.getResources().getColor(R.color.black_80));
                } else {
                    imageView.setImageDrawable(context.getResources().getDrawable(logoRes));
                    textView.setTextColor(context.getResources().getColor(R.color.white));
                    imageViewLeft.setImageDrawable(context.getResources().getDrawable(R.drawable.left));
                    imageViewRight.setImageDrawable(context.getResources().getDrawable(R.drawable.right));
                    tipsTextView.setTextColor(context.getResources().getColor(R.color.white_80));
                }
            }
        });

        parent.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                Log.d("tag", "event:" + event.getAction());
                if (event.getAction() == MotionEvent.ACTION_DOWN) {
                    if (v.isFocusable() && v.isFocusableInTouchMode()) {
                        v.requestFocus();
                    }
                } else if (event.getAction() == MotionEvent.ACTION_UP) {
                    return v.callOnClick();
                }

//                if (event.getAction() == MotionEvent.ACTION_DOWN) {
//                    layoutFocusBg.setFocus(true);
//                    imageView.setImageDrawable(context.getResources().getDrawable(logRes_black));
//                    textView.setTextColor(context.getResources().getColor(R.color.black));
//                    imageViewLeft.setImageDrawable(context.getResources().getDrawable(R.drawable.left_black));
//                    imageViewRight.setImageDrawable(context.getResources().getDrawable(R.drawable.right_black));
//                    tipsTextView.setTextColor(context.getResources().getColor(R.color.black_80));
//                } else if (event.getAction() == MotionEvent.ACTION_UP || event.getAction() == MotionEvent.ACTION_CANCEL) {
//                    layoutFocusBg.setFocus(false);
//                    imageView.setImageDrawable(context.getResources().getDrawable(logoRes));
//                    textView.setTextColor(context.getResources().getColor(R.color.white));
//                    imageViewLeft.setImageDrawable(context.getResources().getDrawable(R.drawable.left));
//                    imageViewRight.setImageDrawable(context.getResources().getDrawable(R.drawable.right));
//                    tipsTextView.setTextColor(context.getResources().getColor(R.color.white_80));
//                }
                return false;
            }
        });
    }

    /**
     * 解绑设备页面
     */
    public static RelativeLayout getDevicesLayout(Context context) {
        RelativeLayout rootView = new RelativeLayout(context);
        rootView.setLayoutParams(new RelativeLayout.LayoutParams(Util.Div(1920), Util.Div(1080)));
//        rootView.setBackground(context.getResources().getDrawable(R.drawable.person_center_background));

        TextView unBindThemeTextView = new TextView(context);
        unBindThemeTextView.setText(R.string.unbind_devices_theme);
        unBindThemeTextView.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
        unBindThemeTextView.setTextColor(context.getResources().getColor(R.color.white));
        unBindThemeTextView.setTextSize(Util.Dpi(56));
        unBindThemeTextView.setSingleLine(true);
        unBindThemeTextView.setGravity(Gravity.CENTER);
        RelativeLayout.LayoutParams unBindThemeRL = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT,
                RelativeLayout.LayoutParams.WRAP_CONTENT);
        unBindThemeRL.topMargin = Util.Div(80 - 4);
        unBindThemeRL.leftMargin = Util.Div(80);
        rootView.addView(unBindThemeTextView, unBindThemeRL);

        TextView menuTextView = new TextView(context);
        menuTextView.setText(R.string.unbind_devices_menu);
        menuTextView.setTextColor(context.getResources().getColor(R.color.white_50));
        menuTextView.setTextSize(Util.Dpi(32));
        menuTextView.setSingleLine(true);
        menuTextView.setGravity(Gravity.CENTER);
        RelativeLayout.LayoutParams menuRL = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT,
                RelativeLayout.LayoutParams.WRAP_CONTENT);
        menuRL.topMargin = Util.Div(104);
        menuRL.leftMargin = Util.Div(324);
        rootView.addView(menuTextView, menuRL);

        NewRecycleLayout<DeviceBean> newRecycleLayout = new NewRecycleLayout<DeviceBean>(context);
        newRecycleLayout.setOrientation(LinearLayout.VERTICAL);
        newRecycleLayout.setSpanCount(4);
        newRecycleLayout.setClipChildren(false);
        newRecycleLayout.setClipToPadding(false);
        newRecycleLayout.setItemSpace(Util.Div(30), Util.Div(30));
        newRecycleLayout.setPadding(0, 0, Util.Div(30), Util.Div(30));
        newRecycleLayout.setId(R.id.unbind_devices_recycleview);

        RelativeLayout.LayoutParams newRecycleLayoutRL = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT,
                RelativeLayout.LayoutParams.WRAP_CONTENT);
        newRecycleLayoutRL.topMargin = Util.Div(176 - 20);
//        newRecycleLayoutRL.leftMargin = Util.Div(80-35);
        newRecycleLayoutRL.addRule(RelativeLayout.CENTER_HORIZONTAL);
        rootView.addView(newRecycleLayout, newRecycleLayoutRL);

        RelativeLayout noDevicesLayout = new RelativeLayout(context);
        noDevicesLayout.setId(R.id.unbind_devices_no_devices_layout);
        noDevicesLayout.setLayoutParams(new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT,
                RelativeLayout.LayoutParams.MATCH_PARENT));
        rootView.addView(noDevicesLayout);

        TextView noDevicesTpisTextView = new TextView(context);
        noDevicesTpisTextView.setText(R.string.unbind_devices_nodevices_tips);
        noDevicesTpisTextView.setTextColor(context.getResources().getColor(R.color.white));
        noDevicesTpisTextView.setTextSize(Util.Dpi(40));
        noDevicesTpisTextView.setGravity(Gravity.CENTER);
        RelativeLayout.LayoutParams noDevicesTpisRL = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT,
                RelativeLayout.LayoutParams.WRAP_CONTENT);
        noDevicesTpisRL.addRule(RelativeLayout.CENTER_IN_PARENT);
        noDevicesLayout.addView(noDevicesTpisTextView, noDevicesTpisRL);


        CommonFocusBox addDeviceTextViewLayout = new CommonFocusBox(context);
        addDeviceTextViewLayout.setId(R.id.unbind_devices_add_device_btn);
        addDeviceTextViewLayout.setCornerRadius(Util.Div(53));
        RelativeLayout.LayoutParams addDeviceTextViewLayoutRL = new RelativeLayout.LayoutParams(Util.Div(460 + 10), Util.Div(80 + 10));
        addDeviceTextViewLayoutRL.leftMargin = Util.Div(730 - 8);
        addDeviceTextViewLayoutRL.topMargin = Util.Div(700 - 8);
        noDevicesLayout.addView(addDeviceTextViewLayout, addDeviceTextViewLayoutRL);

        TextView addDeviceTextView = new TextView(context);
        addDeviceTextView.setId(R.id.unbind_devices_add_device);
        addDeviceTextView.setText(R.string.unbind_devices_add_device);
        addDeviceTextView.setTextColor(context.getResources().getColor(R.color.white));
        addDeviceTextView.setTextSize(Util.Dpi(36));
        addDeviceTextView.setGravity(Gravity.CENTER);
//        addDeviceTextView.setBackground(getFocused(context, Util.Div(40)));
        FrameLayout.LayoutParams addDeviceRL = new FrameLayout.LayoutParams(Util.Div(460), Util.Div(80));
        addDeviceRL.gravity = Gravity.CENTER;
        addDeviceTextViewLayout.addView(addDeviceTextView, addDeviceRL);

        noDevicesLayout.setVisibility(View.INVISIBLE);

        return rootView;
    }

    /**
     * 按menu按键，弹出解绑页面mask界面
     */
    public static RelativeLayout getUnbindItemMenuPage(Context context) {

        RelativeLayout rootView = new RelativeLayout(context);
        rootView.setBackground(XThemeUtils.getDrawable(context.getResources().getColor(R.color.black_50), 0, 0, Util.Div(8)));
        rootView.setLayoutParams(new RelativeLayout.LayoutParams(Util.Div(410), Util.Div(246)));

        GradientDrawable gradientDrawable = new GradientDrawable();
        gradientDrawable.setShape(GradientDrawable.RECTANGLE);
        gradientDrawable.setColor(context.getResources().getColor(R.color.color_FE3030));
        gradientDrawable.setCornerRadius(Util.Div(30));

        RelativeLayout unbindLayout = new RelativeLayout(context);
        unbindLayout.setBackground(gradientDrawable);
        RelativeLayout.LayoutParams unBindRL = new RelativeLayout.LayoutParams(Util.Div(160), Util.Div(60));
        unBindRL.addRule(RelativeLayout.CENTER_IN_PARENT);
        rootView.addView(unbindLayout, unBindRL);

        ImageView unbindImageView = new ImageView(context);
        unbindImageView.setImageDrawable(context.getResources().getDrawable(R.drawable.unbind_logo));
        RelativeLayout.LayoutParams unbindImageViewRL = new RelativeLayout.LayoutParams(Util.Div(30), Util.Div(30));
        unbindImageViewRL.leftMargin = Util.Div(32);
        unbindImageViewRL.addRule(RelativeLayout.CENTER_VERTICAL);
        unbindLayout.addView(unbindImageView, unbindImageViewRL);

        TextView unbindTextView = new TextView(context);
        unbindTextView.setId(R.id.unbind_devices_delete);
        unbindTextView.setText(context.getResources().getString(R.string.unbind_devices_unbind));
        unbindTextView.setTextColor(context.getResources().getColor(R.color.white));
        unbindTextView.setTextSize(Util.Dpi(28));
        RelativeLayout.LayoutParams unbindTextViewRL = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT,
                RelativeLayout.LayoutParams.WRAP_CONTENT);
        unbindTextViewRL.leftMargin = Util.Div(72);
        unbindTextViewRL.addRule(RelativeLayout.CENTER_VERTICAL);
        unbindLayout.addView(unbindTextView, unbindTextViewRL);

        return rootView;
    }

    /**
     * 解绑页面的dialog的View
     */
    public static RelativeLayout getUnBindDialogView(final Context context) {
        RelativeLayout parent = new RelativeLayout(context);
        parent.setLayoutParams(new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT,
                RelativeLayout.LayoutParams.WRAP_CONTENT));

        RelativeLayout rootView = new RelativeLayout(context);
        parent.addView(rootView, new RelativeLayout.LayoutParams(Util.Div(714), Util.Div(350)));
        //设置背景
        GradientDrawable rootViewBackGroudDrawable = new GradientDrawable();
        rootViewBackGroudDrawable.setShape(GradientDrawable.RECTANGLE);
        rootViewBackGroudDrawable.setColor(context.getResources().getColor(R.color.color_3E4148));
        rootViewBackGroudDrawable.setCornerRadius(Util.Div(16));
        rootView.setBackground(rootViewBackGroudDrawable);

        TextView themeTextView = new TextView(context);
        themeTextView.setText(R.string.unbind_devices_dialog_theme);
        themeTextView.setTextColor(context.getResources().getColor(R.color.white));
        themeTextView.setTextSize(Util.Dpi(36));
        themeTextView.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
        themeTextView.setSingleLine(false);
        themeTextView.setLineSpacing(Util.Div(55), 0);
        themeTextView.setGravity(Gravity.CENTER_VERTICAL);
        RelativeLayout.LayoutParams themeRL = new RelativeLayout.LayoutParams(Util.Div(614),
                Util.Div(100));
        themeRL.topMargin = Util.Div(31 + 5);
        themeRL.addRule(RelativeLayout.CENTER_HORIZONTAL);
        rootView.addView(themeTextView, themeRL);

        CommonFocusBox unBindSureBtnLayout = new CommonFocusBox(context);
        unBindSureBtnLayout.setId(R.id.unbind_devices_btn_sure);
        RelativeLayout.LayoutParams unBindSureBtnLayoutRL = new RelativeLayout.LayoutParams(Util.Div(292 + 10), Util.Div(90 + 10));
        unBindSureBtnLayoutRL.topMargin = Util.Div(210 - 8);
        unBindSureBtnLayoutRL.leftMargin = Util.Div(50 - 8);
        rootView.addView(unBindSureBtnLayout, unBindSureBtnLayoutRL);

        final TextView unBindSureBtn = new TextView(context);
        unBindSureBtn.setText(R.string.offline_help_sure);
        unBindSureBtn.setTextColor(context.getResources().getColor(R.color.white_60));
        unBindSureBtn.setTextSize(Util.Dpi(32));
        unBindSureBtn.setSingleLine(true);
        unBindSureBtn.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
        unBindSureBtn.setGravity(Gravity.CENTER);
//        unBindSureBtn.setBackground(getNoFocused(context,Util.Div(16)));

        FrameLayout.LayoutParams unBindSureBtnRL = new FrameLayout.LayoutParams(Util.Div(292), Util.Div(90));
        unBindSureBtnRL.gravity = Gravity.CENTER;
        unBindSureBtnLayout.addView(unBindSureBtn, unBindSureBtnRL);

        unBindSureBtnLayout.setOnFocusChangeListener(new CommonFocusBox.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    unBindSureBtn.setTextColor(context.getResources().getColor(R.color.black));
//                    unBindSureBtn.setBackground(getFocused(context,Util.Div(16)));
                } else {
                    unBindSureBtn.setTextColor(context.getResources().getColor(R.color.white_60));
//                    unBindSureBtn.setBackground(getNoFocused(context,Util.Div(16)));
                }
            }
        });

        unBindSureBtnLayout.setOnTouchListener(new CommonFocusBox.OnTouchListener() {
            @Override
            public void onTouch(View v, MotionEvent event) {
                if (event.getAction() == MotionEvent.ACTION_DOWN) {
                    unBindSureBtn.setTextColor(context.getResources().getColor(R.color.black));
//                    unBindSureBtn.setBackground(getFocused(context,Util.Div(16)));
                } else if (event.getAction() == MotionEvent.ACTION_UP || event.getAction() == MotionEvent.ACTION_CANCEL) {
                    unBindSureBtn.setTextColor(context.getResources().getColor(R.color.white_60));
//                    unBindSureBtn.setBackground(getNoFocused(context,Util.Div(16)));
                }
            }
        });


        CommonFocusBox unBindCancleBtnLayout = new CommonFocusBox(context);
        unBindCancleBtnLayout.setId(R.id.unbind_devices_btn_cancle);
        RelativeLayout.LayoutParams unBindCancleBtnLayoutRL = new RelativeLayout.LayoutParams(Util.Div(292 + 10), Util.Div(90 + 10));
        unBindCancleBtnLayoutRL.topMargin = Util.Div(210 - 8);
        unBindCancleBtnLayoutRL.leftMargin = Util.Div(372 - 8);
        rootView.addView(unBindCancleBtnLayout, unBindCancleBtnLayoutRL);

        final TextView unBindCancleBtn = new TextView(context);
        unBindCancleBtn.setText(R.string.offline_help_cancle);
        unBindCancleBtn.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
        unBindCancleBtn.setTextColor(context.getResources().getColor(R.color.white_60));
        unBindCancleBtn.setTextSize(Util.Dpi(32));
        unBindCancleBtn.setSingleLine(true);
        unBindCancleBtn.setGravity(Gravity.CENTER);
//        unBindCancleBtn.setBackground(getNoFocused(context,Util.Div(16)));
        FrameLayout.LayoutParams unBindCancleRL = new FrameLayout.LayoutParams(Util.Div(292), Util.Div(90));
        unBindCancleRL.gravity = Gravity.CENTER;
        unBindCancleBtnLayout.addView(unBindCancleBtn, unBindCancleRL);

        unBindCancleBtnLayout.setOnFocusChangeListener(new CommonFocusBox.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    unBindCancleBtn.setTextColor(context.getResources().getColor(R.color.black));
//                    unBindCancleBtn.setBackground(getFocused(context,Util.Div(16)));
                } else {
                    unBindCancleBtn.setTextColor(context.getResources().getColor(R.color.white_60));
//                    unBindCancleBtn.setBackground(getNoFocused(context,Util.Div(16)));
                }
            }
        });

        unBindCancleBtnLayout.setOnTouchListener(new CommonFocusBox.OnTouchListener() {
            @Override
            public void onTouch(View v, MotionEvent event) {
                if (event.getAction() == MotionEvent.ACTION_DOWN) {
                    unBindCancleBtn.setTextColor(context.getResources().getColor(R.color.black));
//                    unBindCancleBtn.setBackground(getFocused(context,Util.Div(16)));
                } else if (event.getAction() == MotionEvent.ACTION_UP || event.getAction() == MotionEvent.ACTION_CANCEL) {
                    unBindCancleBtn.setTextColor(context.getResources().getColor(R.color.white_10));
//                    unBindCancleBtn.setBackground(getNoFocused(context,Util.Div(16)));
                }
            }
        });

        return parent;
    }


    private static StateListDrawable getStateListDrawable(Context context, int radius) {
        return XThemeUtils.getNormalBgDrawable(context, radius);
    }

    private static StateListDrawable getStateListDrawable2(Context context, int radius) {
        GradientDrawable nonFocusedDrawable = new GradientDrawable();
        nonFocusedDrawable.setShape(GradientDrawable.RECTANGLE);
        nonFocusedDrawable.setColor(context.getResources().getColor(R.color.white_10));
        nonFocusedDrawable.setCornerRadius(radius);

        GradientDrawable FocusedDrawable = new GradientDrawable();
        FocusedDrawable.setShape(GradientDrawable.RECTANGLE);
        FocusedDrawable.setColor(context.getResources().getColor(R.color.white));
        FocusedDrawable.setCornerRadius(radius);

        StateListDrawable stateListDrawable = new StateListDrawable();
        //Non focused states
        stateListDrawable.addState(new int[]{-android.R.attr.state_focused, -android.R.attr.state_selected, -android.R.attr.state_pressed},
                nonFocusedDrawable);
        stateListDrawable.addState(new int[]{-android.R.attr.state_focused, android.R.attr.state_selected, -android.R.attr.state_pressed},
                nonFocusedDrawable);
        //Focused states
        stateListDrawable.addState(new int[]{android.R.attr.state_focused, -android.R.attr.state_selected, -android.R.attr.state_pressed},
                FocusedDrawable);
        stateListDrawable.addState(new int[]{android.R.attr.state_focused, android.R.attr.state_selected, -android.R.attr.state_pressed},
                FocusedDrawable);
        //Pressed
        stateListDrawable.addState(new int[]{android.R.attr.state_selected, android.R.attr.state_pressed},
                FocusedDrawable);
        stateListDrawable.addState(new int[]{android.R.attr.state_pressed},
                FocusedDrawable);

        return stateListDrawable;
    }

    private static GradientDrawable getNoFocused(Context context, int radius) {
        GradientDrawable nonFocusedDrawable = new GradientDrawable();
        nonFocusedDrawable.setShape(GradientDrawable.RECTANGLE);
        nonFocusedDrawable.setColor(context.getResources().getColor(R.color.white_10));
        nonFocusedDrawable.setCornerRadius(radius);

        return nonFocusedDrawable;
    }

    private static GradientDrawable getFocused(Context context, int radius) {
        GradientDrawable FocusedDrawable = new GradientDrawable();
        FocusedDrawable.setShape(GradientDrawable.RECTANGLE);
        FocusedDrawable.setColor(context.getResources().getColor(R.color.white));
        FocusedDrawable.setCornerRadius(radius);

        return FocusedDrawable;
    }


    /**
     * toast样式
     */
    public static RelativeLayout getToastLayout(Context context) {
        RelativeLayout parent = new RelativeLayout(context);
        parent.setLayoutParams(new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT,
                RelativeLayout.LayoutParams.WRAP_CONTENT));

        RelativeLayout rootView = new RelativeLayout(context);
        rootView.setBackground(context.getResources().getDrawable(R.drawable.toast_bg));
        rootView.setPadding(Util.Div(30), Util.Div(19), Util.Div(30), Util.Div(25));
        parent.addView(rootView, new RelativeLayout.LayoutParams(Util.Div(456), Util.Div(69)));

        TextView tipsTextView = new TextView(context);
        tipsTextView.setId(R.id.toast_content_id);
        tipsTextView.setText("语音呼出“请帮我开启洗衣机的节能洗”");
        tipsTextView.setTextSize(Util.Dpi(22));
        tipsTextView.setTextColor(context.getResources().getColor(R.color.black_60));
        tipsTextView.setGravity(Gravity.CENTER);
        tipsTextView.setSingleLine(true);
        tipsTextView.setMarqueeRepeatLimit(-1);

        RelativeLayout.LayoutParams tipsTextViewRL = new RelativeLayout.LayoutParams(
                Util.Div(394), RelativeLayout.LayoutParams.WRAP_CONTENT);
        rootView.addView(tipsTextView, tipsTextViewRL);

        return parent;
    }

}
