/*
 * This file is auto-generated.  DO NOT MODIFY.
 */
package com.swaiot.aiotlib.scene;
// Declare any non-default types here with import statements

public interface ISceneManager extends android.os.IInterface
{
  /** Default implementation for ISceneManager. */
  public static class Default implements com.swaiot.aiotlib.scene.ISceneManager
  {
    @Override public void addScene(java.lang.String commandId) throws android.os.RemoteException
    {
    }
    @Override public void editScene(java.lang.String scene_id, java.lang.String commandId) throws android.os.RemoteException
    {
    }
    @Override public void deleteScene(java.lang.String scene_id, java.lang.String commandId) throws android.os.RemoteException
    {
    }
    @Override
    public android.os.IBinder asBinder() {
      return null;
    }
  }
  /** Local-side IPC implementation stub class. */
  public static abstract class Stub extends android.os.Binder implements com.swaiot.aiotlib.scene.ISceneManager
  {
    private static final java.lang.String DESCRIPTOR = "com.swaiot.aiotlib.scene.ISceneManager";
    /** Construct the stub at attach it to the interface. */
    public Stub()
    {
      this.attachInterface(this, DESCRIPTOR);
    }
    /**
     * Cast an IBinder object into an com.swaiot.aiotlib.scene.ISceneManager interface,
     * generating a proxy if needed.
     */
    public static com.swaiot.aiotlib.scene.ISceneManager asInterface(android.os.IBinder obj)
    {
      if ((obj==null)) {
        return null;
      }
      android.os.IInterface iin = obj.queryLocalInterface(DESCRIPTOR);
      if (((iin!=null)&&(iin instanceof com.swaiot.aiotlib.scene.ISceneManager))) {
        return ((com.swaiot.aiotlib.scene.ISceneManager)iin);
      }
      return new com.swaiot.aiotlib.scene.ISceneManager.Stub.Proxy(obj);
    }
    @Override public android.os.IBinder asBinder()
    {
      return this;
    }
    @Override public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws android.os.RemoteException
    {
      java.lang.String descriptor = DESCRIPTOR;
      switch (code)
      {
        case INTERFACE_TRANSACTION:
        {
          reply.writeString(descriptor);
          return true;
        }
        case TRANSACTION_addScene:
        {
          data.enforceInterface(descriptor);
          java.lang.String _arg0;
          _arg0 = data.readString();
          this.addScene(_arg0);
          reply.writeNoException();
          return true;
        }
        case TRANSACTION_editScene:
        {
          data.enforceInterface(descriptor);
          java.lang.String _arg0;
          _arg0 = data.readString();
          java.lang.String _arg1;
          _arg1 = data.readString();
          this.editScene(_arg0, _arg1);
          reply.writeNoException();
          return true;
        }
        case TRANSACTION_deleteScene:
        {
          data.enforceInterface(descriptor);
          java.lang.String _arg0;
          _arg0 = data.readString();
          java.lang.String _arg1;
          _arg1 = data.readString();
          this.deleteScene(_arg0, _arg1);
          reply.writeNoException();
          return true;
        }
        default:
        {
          return super.onTransact(code, data, reply, flags);
        }
      }
    }
    private static class Proxy implements com.swaiot.aiotlib.scene.ISceneManager
    {
      private android.os.IBinder mRemote;
      Proxy(android.os.IBinder remote)
      {
        mRemote = remote;
      }
      @Override public android.os.IBinder asBinder()
      {
        return mRemote;
      }
      public java.lang.String getInterfaceDescriptor()
      {
        return DESCRIPTOR;
      }
      @Override public void addScene(java.lang.String commandId) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(commandId);
          boolean _status = mRemote.transact(Stub.TRANSACTION_addScene, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().addScene(commandId);
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public void editScene(java.lang.String scene_id, java.lang.String commandId) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(scene_id);
          _data.writeString(commandId);
          boolean _status = mRemote.transact(Stub.TRANSACTION_editScene, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().editScene(scene_id, commandId);
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public void deleteScene(java.lang.String scene_id, java.lang.String commandId) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(scene_id);
          _data.writeString(commandId);
          boolean _status = mRemote.transact(Stub.TRANSACTION_deleteScene, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().deleteScene(scene_id, commandId);
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      public static com.swaiot.aiotlib.scene.ISceneManager sDefaultImpl;
    }
    static final int TRANSACTION_addScene = (android.os.IBinder.FIRST_CALL_TRANSACTION + 0);
    static final int TRANSACTION_editScene = (android.os.IBinder.FIRST_CALL_TRANSACTION + 1);
    static final int TRANSACTION_deleteScene = (android.os.IBinder.FIRST_CALL_TRANSACTION + 2);
    public static boolean setDefaultImpl(com.swaiot.aiotlib.scene.ISceneManager impl) {
      if (Stub.Proxy.sDefaultImpl == null && impl != null) {
        Stub.Proxy.sDefaultImpl = impl;
        return true;
      }
      return false;
    }
    public static com.swaiot.aiotlib.scene.ISceneManager getDefaultImpl() {
      return Stub.Proxy.sDefaultImpl;
    }
  }
  public void addScene(java.lang.String commandId) throws android.os.RemoteException;
  public void editScene(java.lang.String scene_id, java.lang.String commandId) throws android.os.RemoteException;
  public void deleteScene(java.lang.String scene_id, java.lang.String commandId) throws android.os.RemoteException;
}
