package com.swaiot.aiotlib.service.binder.device;

import android.os.RemoteException;

import com.swaiot.aiotlib.common.model.AiotConstants;
import com.swaiot.aiotlib.common.util.EmptyUtils;
import com.swaiot.aiotlib.common.util.LogUtil;
import com.swaiot.aiotlib.common.util.ThreadManager;
import com.swaiot.aiotlib.device.IDeviceControl;
import com.swaiot.lib.SkyAIOTContract;

/**
 * @ClassName: DeviceControlBinder
 * @Author: AwenZeng
 * @CreateDate: 2020/5/8 18:52
 * @Description:
 */
public class DeviceControlBinder extends IDeviceControl.Stub {

    @Override
    public boolean controlDevice(String device_id, String param, String commandId) throws RemoteException {
        SkyAIOTContract.control_object(AiotConstants.KEY_HTTP_CONTROL_DEVICE, EmptyUtils.handleNullString(device_id), EmptyUtils.handleNullString(param), EmptyUtils.handleNullString(commandId));
        return true;
    }

    @Override
    public void cancelNewDeviceMark(String device_id, String commandId) throws RemoteException {
        LogUtil.androidLog( "cancelNewDeviceMark() called with: device_id = [" + device_id + "], commandId = [" + commandId + "]");
        SkyAIOTContract.control_object(AiotConstants.KEY_CANCEL_NEW_DEVICE_TAG, EmptyUtils.handleNullString(device_id), "", EmptyUtils.handleNullString(commandId));
    }
}
