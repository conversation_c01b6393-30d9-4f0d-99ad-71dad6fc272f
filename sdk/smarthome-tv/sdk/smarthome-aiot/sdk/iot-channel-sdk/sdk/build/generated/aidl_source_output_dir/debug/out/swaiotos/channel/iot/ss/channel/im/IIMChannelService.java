/*
 * This file is auto-generated.  DO NOT MODIFY.
 */
package swaiotos.channel.iot.ss.channel.im;
public interface IIMChannelService extends android.os.IInterface
{
/** Local-side IPC implementation stub class. */
public static abstract class Stub extends android.os.Binder implements swaiotos.channel.iot.ss.channel.im.IIMChannelService
{
private static final java.lang.String DESCRIPTOR = "swaiotos.channel.iot.ss.channel.im.IIMChannelService";
/** Construct the stub at attach it to the interface. */
public Stub()
{
this.attachInterface(this, DESCRIPTOR);
}
/**
 * Cast an IBinder object into an swaiotos.channel.iot.ss.channel.im.IIMChannelService interface,
 * generating a proxy if needed.
 */
public static swaiotos.channel.iot.ss.channel.im.IIMChannelService asInterface(android.os.IBinder obj)
{
if ((obj==null)) {
return null;
}
android.os.IInterface iin = obj.queryLocalInterface(DESCRIPTOR);
if (((iin!=null)&&(iin instanceof swaiotos.channel.iot.ss.channel.im.IIMChannelService))) {
return ((swaiotos.channel.iot.ss.channel.im.IIMChannelService)iin);
}
return new swaiotos.channel.iot.ss.channel.im.IIMChannelService.Stub.Proxy(obj);
}
@Override public android.os.IBinder asBinder()
{
return this;
}
@Override public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws android.os.RemoteException
{
java.lang.String descriptor = DESCRIPTOR;
switch (code)
{
case INTERFACE_TRANSACTION:
{
reply.writeString(descriptor);
return true;
}
case TRANSACTION_send:
{
data.enforceInterface(descriptor);
swaiotos.channel.iot.ss.channel.im.IMMessage _arg0;
if ((0!=data.readInt())) {
_arg0 = swaiotos.channel.iot.ss.channel.im.IMMessage.CREATOR.createFromParcel(data);
}
else {
_arg0 = null;
}
android.os.Messenger _arg1;
if ((0!=data.readInt())) {
_arg1 = android.os.Messenger.CREATOR.createFromParcel(data);
}
else {
_arg1 = null;
}
this.send(_arg0, _arg1);
reply.writeNoException();
return true;
}
case TRANSACTION_sendSync:
{
data.enforceInterface(descriptor);
swaiotos.channel.iot.ss.channel.im.IMMessage _arg0;
if ((0!=data.readInt())) {
_arg0 = swaiotos.channel.iot.ss.channel.im.IMMessage.CREATOR.createFromParcel(data);
}
else {
_arg0 = null;
}
android.os.Messenger _arg1;
if ((0!=data.readInt())) {
_arg1 = android.os.Messenger.CREATOR.createFromParcel(data);
}
else {
_arg1 = null;
}
long _arg2;
_arg2 = data.readLong();
swaiotos.channel.iot.utils.ipc.ParcelableObject _result = this.sendSync(_arg0, _arg1, _arg2);
reply.writeNoException();
if ((_result!=null)) {
reply.writeInt(1);
_result.writeToParcel(reply, android.os.Parcelable.PARCELABLE_WRITE_RETURN_VALUE);
}
else {
reply.writeInt(0);
}
return true;
}
case TRANSACTION_reset:
{
data.enforceInterface(descriptor);
java.lang.String _arg0;
_arg0 = data.readString();
java.lang.String _arg1;
_arg1 = data.readString();
this.reset(_arg0, _arg1);
reply.writeNoException();
return true;
}
case TRANSACTION_sendBroadCast:
{
data.enforceInterface(descriptor);
swaiotos.channel.iot.ss.channel.im.IMMessage _arg0;
if ((0!=data.readInt())) {
_arg0 = swaiotos.channel.iot.ss.channel.im.IMMessage.CREATOR.createFromParcel(data);
}
else {
_arg0 = null;
}
android.os.Messenger _arg1;
if ((0!=data.readInt())) {
_arg1 = android.os.Messenger.CREATOR.createFromParcel(data);
}
else {
_arg1 = null;
}
this.sendBroadCast(_arg0, _arg1);
reply.writeNoException();
return true;
}
default:
{
return super.onTransact(code, data, reply, flags);
}
}
}
private static class Proxy implements swaiotos.channel.iot.ss.channel.im.IIMChannelService
{
private android.os.IBinder mRemote;
Proxy(android.os.IBinder remote)
{
mRemote = remote;
}
@Override public android.os.IBinder asBinder()
{
return mRemote;
}
public java.lang.String getInterfaceDescriptor()
{
return DESCRIPTOR;
}
@Override public void send(swaiotos.channel.iot.ss.channel.im.IMMessage message, android.os.Messenger callback) throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
try {
_data.writeInterfaceToken(DESCRIPTOR);
if ((message!=null)) {
_data.writeInt(1);
message.writeToParcel(_data, 0);
}
else {
_data.writeInt(0);
}
if ((callback!=null)) {
_data.writeInt(1);
callback.writeToParcel(_data, 0);
}
else {
_data.writeInt(0);
}
mRemote.transact(Stub.TRANSACTION_send, _data, _reply, 0);
_reply.readException();
}
finally {
_reply.recycle();
_data.recycle();
}
}
@Override public swaiotos.channel.iot.utils.ipc.ParcelableObject sendSync(swaiotos.channel.iot.ss.channel.im.IMMessage message, android.os.Messenger callback, long timeout) throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
swaiotos.channel.iot.utils.ipc.ParcelableObject _result;
try {
_data.writeInterfaceToken(DESCRIPTOR);
if ((message!=null)) {
_data.writeInt(1);
message.writeToParcel(_data, 0);
}
else {
_data.writeInt(0);
}
if ((callback!=null)) {
_data.writeInt(1);
callback.writeToParcel(_data, 0);
}
else {
_data.writeInt(0);
}
_data.writeLong(timeout);
mRemote.transact(Stub.TRANSACTION_sendSync, _data, _reply, 0);
_reply.readException();
if ((0!=_reply.readInt())) {
_result = swaiotos.channel.iot.utils.ipc.ParcelableObject.CREATOR.createFromParcel(_reply);
}
else {
_result = null;
}
}
finally {
_reply.recycle();
_data.recycle();
}
return _result;
}
@Override public void reset(java.lang.String sid, java.lang.String token) throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
try {
_data.writeInterfaceToken(DESCRIPTOR);
_data.writeString(sid);
_data.writeString(token);
mRemote.transact(Stub.TRANSACTION_reset, _data, _reply, 0);
_reply.readException();
}
finally {
_reply.recycle();
_data.recycle();
}
}
@Override public void sendBroadCast(swaiotos.channel.iot.ss.channel.im.IMMessage message, android.os.Messenger callback) throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
try {
_data.writeInterfaceToken(DESCRIPTOR);
if ((message!=null)) {
_data.writeInt(1);
message.writeToParcel(_data, 0);
}
else {
_data.writeInt(0);
}
if ((callback!=null)) {
_data.writeInt(1);
callback.writeToParcel(_data, 0);
}
else {
_data.writeInt(0);
}
mRemote.transact(Stub.TRANSACTION_sendBroadCast, _data, _reply, 0);
_reply.readException();
}
finally {
_reply.recycle();
_data.recycle();
}
}
}
static final int TRANSACTION_send = (android.os.IBinder.FIRST_CALL_TRANSACTION + 0);
static final int TRANSACTION_sendSync = (android.os.IBinder.FIRST_CALL_TRANSACTION + 1);
static final int TRANSACTION_reset = (android.os.IBinder.FIRST_CALL_TRANSACTION + 2);
static final int TRANSACTION_sendBroadCast = (android.os.IBinder.FIRST_CALL_TRANSACTION + 3);
}
public void send(swaiotos.channel.iot.ss.channel.im.IMMessage message, android.os.Messenger callback) throws android.os.RemoteException;
public swaiotos.channel.iot.utils.ipc.ParcelableObject sendSync(swaiotos.channel.iot.ss.channel.im.IMMessage message, android.os.Messenger callback, long timeout) throws android.os.RemoteException;
public void reset(java.lang.String sid, java.lang.String token) throws android.os.RemoteException;
public void sendBroadCast(swaiotos.channel.iot.ss.channel.im.IMMessage message, android.os.Messenger callback) throws android.os.RemoteException;
}
