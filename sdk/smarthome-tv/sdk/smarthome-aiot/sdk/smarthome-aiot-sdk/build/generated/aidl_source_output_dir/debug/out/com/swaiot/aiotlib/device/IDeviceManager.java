/*
 * This file is auto-generated.  DO NOT MODIFY.
 */
package com.swaiot.aiotlib.device;
// Declare any non-default types here with import statements

public interface IDeviceManager extends android.os.IInterface
{
  /** Default implementation for IDeviceManager. */
  public static class Default implements com.swaiot.aiotlib.device.IDeviceManager
  {
    @Override public void getBindStatus(java.lang.String deviceParam, java.lang.String commandId) throws android.os.RemoteException
    {
    }
    @Override public void bindDevice(java.lang.String deviceId, java.lang.String param, java.lang.String commandId) throws android.os.RemoteException
    {
    }
    @Override public void unBindDevice(java.lang.String devicesId, java.lang.String commandId) throws android.os.RemoteException
    {
    }
    @Override public void forceUnBindDevice(java.lang.String deviceId, java.lang.String commandId) throws android.os.RemoteException
    {
    }
    @Override
    public android.os.IBinder asBinder() {
      return null;
    }
  }
  /** Local-side IPC implementation stub class. */
  public static abstract class Stub extends android.os.Binder implements com.swaiot.aiotlib.device.IDeviceManager
  {
    private static final java.lang.String DESCRIPTOR = "com.swaiot.aiotlib.device.IDeviceManager";
    /** Construct the stub at attach it to the interface. */
    public Stub()
    {
      this.attachInterface(this, DESCRIPTOR);
    }
    /**
     * Cast an IBinder object into an com.swaiot.aiotlib.device.IDeviceManager interface,
     * generating a proxy if needed.
     */
    public static com.swaiot.aiotlib.device.IDeviceManager asInterface(android.os.IBinder obj)
    {
      if ((obj==null)) {
        return null;
      }
      android.os.IInterface iin = obj.queryLocalInterface(DESCRIPTOR);
      if (((iin!=null)&&(iin instanceof com.swaiot.aiotlib.device.IDeviceManager))) {
        return ((com.swaiot.aiotlib.device.IDeviceManager)iin);
      }
      return new com.swaiot.aiotlib.device.IDeviceManager.Stub.Proxy(obj);
    }
    @Override public android.os.IBinder asBinder()
    {
      return this;
    }
    @Override public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws android.os.RemoteException
    {
      java.lang.String descriptor = DESCRIPTOR;
      switch (code)
      {
        case INTERFACE_TRANSACTION:
        {
          reply.writeString(descriptor);
          return true;
        }
        case TRANSACTION_getBindStatus:
        {
          data.enforceInterface(descriptor);
          java.lang.String _arg0;
          _arg0 = data.readString();
          java.lang.String _arg1;
          _arg1 = data.readString();
          this.getBindStatus(_arg0, _arg1);
          reply.writeNoException();
          return true;
        }
        case TRANSACTION_bindDevice:
        {
          data.enforceInterface(descriptor);
          java.lang.String _arg0;
          _arg0 = data.readString();
          java.lang.String _arg1;
          _arg1 = data.readString();
          java.lang.String _arg2;
          _arg2 = data.readString();
          this.bindDevice(_arg0, _arg1, _arg2);
          reply.writeNoException();
          return true;
        }
        case TRANSACTION_unBindDevice:
        {
          data.enforceInterface(descriptor);
          java.lang.String _arg0;
          _arg0 = data.readString();
          java.lang.String _arg1;
          _arg1 = data.readString();
          this.unBindDevice(_arg0, _arg1);
          reply.writeNoException();
          return true;
        }
        case TRANSACTION_forceUnBindDevice:
        {
          data.enforceInterface(descriptor);
          java.lang.String _arg0;
          _arg0 = data.readString();
          java.lang.String _arg1;
          _arg1 = data.readString();
          this.forceUnBindDevice(_arg0, _arg1);
          reply.writeNoException();
          return true;
        }
        default:
        {
          return super.onTransact(code, data, reply, flags);
        }
      }
    }
    private static class Proxy implements com.swaiot.aiotlib.device.IDeviceManager
    {
      private android.os.IBinder mRemote;
      Proxy(android.os.IBinder remote)
      {
        mRemote = remote;
      }
      @Override public android.os.IBinder asBinder()
      {
        return mRemote;
      }
      public java.lang.String getInterfaceDescriptor()
      {
        return DESCRIPTOR;
      }
      @Override public void getBindStatus(java.lang.String deviceParam, java.lang.String commandId) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(deviceParam);
          _data.writeString(commandId);
          boolean _status = mRemote.transact(Stub.TRANSACTION_getBindStatus, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().getBindStatus(deviceParam, commandId);
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public void bindDevice(java.lang.String deviceId, java.lang.String param, java.lang.String commandId) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(deviceId);
          _data.writeString(param);
          _data.writeString(commandId);
          boolean _status = mRemote.transact(Stub.TRANSACTION_bindDevice, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().bindDevice(deviceId, param, commandId);
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public void unBindDevice(java.lang.String devicesId, java.lang.String commandId) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(devicesId);
          _data.writeString(commandId);
          boolean _status = mRemote.transact(Stub.TRANSACTION_unBindDevice, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().unBindDevice(devicesId, commandId);
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public void forceUnBindDevice(java.lang.String deviceId, java.lang.String commandId) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(deviceId);
          _data.writeString(commandId);
          boolean _status = mRemote.transact(Stub.TRANSACTION_forceUnBindDevice, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().forceUnBindDevice(deviceId, commandId);
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      public static com.swaiot.aiotlib.device.IDeviceManager sDefaultImpl;
    }
    static final int TRANSACTION_getBindStatus = (android.os.IBinder.FIRST_CALL_TRANSACTION + 0);
    static final int TRANSACTION_bindDevice = (android.os.IBinder.FIRST_CALL_TRANSACTION + 1);
    static final int TRANSACTION_unBindDevice = (android.os.IBinder.FIRST_CALL_TRANSACTION + 2);
    static final int TRANSACTION_forceUnBindDevice = (android.os.IBinder.FIRST_CALL_TRANSACTION + 3);
    public static boolean setDefaultImpl(com.swaiot.aiotlib.device.IDeviceManager impl) {
      if (Stub.Proxy.sDefaultImpl == null && impl != null) {
        Stub.Proxy.sDefaultImpl = impl;
        return true;
      }
      return false;
    }
    public static com.swaiot.aiotlib.device.IDeviceManager getDefaultImpl() {
      return Stub.Proxy.sDefaultImpl;
    }
  }
  public void getBindStatus(java.lang.String deviceParam, java.lang.String commandId) throws android.os.RemoteException;
  public void bindDevice(java.lang.String deviceId, java.lang.String param, java.lang.String commandId) throws android.os.RemoteException;
  public void unBindDevice(java.lang.String devicesId, java.lang.String commandId) throws android.os.RemoteException;
  public void forceUnBindDevice(java.lang.String deviceId, java.lang.String commandId) throws android.os.RemoteException;
}
