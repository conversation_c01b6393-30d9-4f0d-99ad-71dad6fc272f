/*
 * This file is auto-generated.  DO NOT MODIFY.
 */
package swaiotos.channel.iot.ss.device;
public interface IDeviceManagerService extends android.os.IInterface
{
/** Local-side IPC implementation stub class. */
public static abstract class Stub extends android.os.Binder implements swaiotos.channel.iot.ss.device.IDeviceManagerService
{
private static final java.lang.String DESCRIPTOR = "swaiotos.channel.iot.ss.device.IDeviceManagerService";
/** Construct the stub at attach it to the interface. */
public Stub()
{
this.attachInterface(this, DESCRIPTOR);
}
/**
 * Cast an IBinder object into an swaiotos.channel.iot.ss.device.IDeviceManagerService interface,
 * generating a proxy if needed.
 */
public static swaiotos.channel.iot.ss.device.IDeviceManagerService asInterface(android.os.IBinder obj)
{
if ((obj==null)) {
return null;
}
android.os.IInterface iin = obj.queryLocalInterface(DESCRIPTOR);
if (((iin!=null)&&(iin instanceof swaiotos.channel.iot.ss.device.IDeviceManagerService))) {
return ((swaiotos.channel.iot.ss.device.IDeviceManagerService)iin);
}
return new swaiotos.channel.iot.ss.device.IDeviceManagerService.Stub.Proxy(obj);
}
@Override public android.os.IBinder asBinder()
{
return this;
}
@Override public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws android.os.RemoteException
{
java.lang.String descriptor = DESCRIPTOR;
switch (code)
{
case INTERFACE_TRANSACTION:
{
reply.writeString(descriptor);
return true;
}
case TRANSACTION_getDevices:
{
data.enforceInterface(descriptor);
java.util.List<swaiotos.channel.iot.ss.device.Device> _result = this.getDevices();
reply.writeNoException();
reply.writeTypedList(_result);
return true;
}
case TRANSACTION_getDeviceOnlineStatus:
{
data.enforceInterface(descriptor);
java.util.List<swaiotos.channel.iot.ss.device.Device> _result = this.getDeviceOnlineStatus();
reply.writeNoException();
reply.writeTypedList(_result);
return true;
}
case TRANSACTION_getCurrentDevice:
{
data.enforceInterface(descriptor);
swaiotos.channel.iot.ss.device.Device _result = this.getCurrentDevice();
reply.writeNoException();
if ((_result!=null)) {
reply.writeInt(1);
_result.writeToParcel(reply, android.os.Parcelable.PARCELABLE_WRITE_RETURN_VALUE);
}
else {
reply.writeInt(0);
}
return true;
}
case TRANSACTION_addOnDeviceChangedListener:
{
data.enforceInterface(descriptor);
swaiotos.channel.iot.ss.device.IBaseOnDeviceChangedListener _arg0;
_arg0 = swaiotos.channel.iot.ss.device.IBaseOnDeviceChangedListener.Stub.asInterface(data.readStrongBinder());
this.addOnDeviceChangedListener(_arg0);
reply.writeNoException();
return true;
}
case TRANSACTION_removeOnDeviceChangedListener:
{
data.enforceInterface(descriptor);
swaiotos.channel.iot.ss.device.IBaseOnDeviceChangedListener _arg0;
_arg0 = swaiotos.channel.iot.ss.device.IBaseOnDeviceChangedListener.Stub.asInterface(data.readStrongBinder());
this.removeOnDeviceChangedListener(_arg0);
reply.writeNoException();
return true;
}
case TRANSACTION_addDeviceBindListener:
{
data.enforceInterface(descriptor);
swaiotos.channel.iot.ss.device.IDeviceRelationListener _arg0;
_arg0 = swaiotos.channel.iot.ss.device.IDeviceRelationListener.Stub.asInterface(data.readStrongBinder());
this.addDeviceBindListener(_arg0);
reply.writeNoException();
return true;
}
case TRANSACTION_removeDeviceBindListener:
{
data.enforceInterface(descriptor);
swaiotos.channel.iot.ss.device.IDeviceRelationListener _arg0;
_arg0 = swaiotos.channel.iot.ss.device.IDeviceRelationListener.Stub.asInterface(data.readStrongBinder());
this.removeDeviceBindListener(_arg0);
reply.writeNoException();
return true;
}
case TRANSACTION_addDeviceInfoUpdateListener:
{
data.enforceInterface(descriptor);
swaiotos.channel.iot.ss.device.IBaseDeviceInfoUpdateListener _arg0;
_arg0 = swaiotos.channel.iot.ss.device.IBaseDeviceInfoUpdateListener.Stub.asInterface(data.readStrongBinder());
this.addDeviceInfoUpdateListener(_arg0);
reply.writeNoException();
return true;
}
case TRANSACTION_removeDeviceInfoUpdateListener:
{
data.enforceInterface(descriptor);
swaiotos.channel.iot.ss.device.IBaseDeviceInfoUpdateListener _arg0;
_arg0 = swaiotos.channel.iot.ss.device.IBaseDeviceInfoUpdateListener.Stub.asInterface(data.readStrongBinder());
this.removeDeviceInfoUpdateListener(_arg0);
reply.writeNoException();
return true;
}
case TRANSACTION_addDevicesReflushListener:
{
data.enforceInterface(descriptor);
swaiotos.channel.iot.ss.device.IBaseDevicesReflushListener _arg0;
_arg0 = swaiotos.channel.iot.ss.device.IBaseDevicesReflushListener.Stub.asInterface(data.readStrongBinder());
this.addDevicesReflushListener(_arg0);
reply.writeNoException();
return true;
}
case TRANSACTION_removeDevicesReflushListener:
{
data.enforceInterface(descriptor);
swaiotos.channel.iot.ss.device.IBaseDevicesReflushListener _arg0;
_arg0 = swaiotos.channel.iot.ss.device.IBaseDevicesReflushListener.Stub.asInterface(data.readStrongBinder());
this.removeDevicesReflushListener(_arg0);
reply.writeNoException();
return true;
}
case TRANSACTION_updateDeviceList:
{
data.enforceInterface(descriptor);
java.util.List<swaiotos.channel.iot.ss.device.Device> _result = this.updateDeviceList();
reply.writeNoException();
reply.writeTypedList(_result);
return true;
}
case TRANSACTION_getAccessToken:
{
data.enforceInterface(descriptor);
java.lang.String _result = this.getAccessToken();
reply.writeNoException();
reply.writeString(_result);
return true;
}
default:
{
return super.onTransact(code, data, reply, flags);
}
}
}
private static class Proxy implements swaiotos.channel.iot.ss.device.IDeviceManagerService
{
private android.os.IBinder mRemote;
Proxy(android.os.IBinder remote)
{
mRemote = remote;
}
@Override public android.os.IBinder asBinder()
{
return mRemote;
}
public java.lang.String getInterfaceDescriptor()
{
return DESCRIPTOR;
}
@Override public java.util.List<swaiotos.channel.iot.ss.device.Device> getDevices() throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
java.util.List<swaiotos.channel.iot.ss.device.Device> _result;
try {
_data.writeInterfaceToken(DESCRIPTOR);
mRemote.transact(Stub.TRANSACTION_getDevices, _data, _reply, 0);
_reply.readException();
_result = _reply.createTypedArrayList(swaiotos.channel.iot.ss.device.Device.CREATOR);
}
finally {
_reply.recycle();
_data.recycle();
}
return _result;
}
@Override public java.util.List<swaiotos.channel.iot.ss.device.Device> getDeviceOnlineStatus() throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
java.util.List<swaiotos.channel.iot.ss.device.Device> _result;
try {
_data.writeInterfaceToken(DESCRIPTOR);
mRemote.transact(Stub.TRANSACTION_getDeviceOnlineStatus, _data, _reply, 0);
_reply.readException();
_result = _reply.createTypedArrayList(swaiotos.channel.iot.ss.device.Device.CREATOR);
}
finally {
_reply.recycle();
_data.recycle();
}
return _result;
}
@Override public swaiotos.channel.iot.ss.device.Device getCurrentDevice() throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
swaiotos.channel.iot.ss.device.Device _result;
try {
_data.writeInterfaceToken(DESCRIPTOR);
mRemote.transact(Stub.TRANSACTION_getCurrentDevice, _data, _reply, 0);
_reply.readException();
if ((0!=_reply.readInt())) {
_result = swaiotos.channel.iot.ss.device.Device.CREATOR.createFromParcel(_reply);
}
else {
_result = null;
}
}
finally {
_reply.recycle();
_data.recycle();
}
return _result;
}
@Override public void addOnDeviceChangedListener(swaiotos.channel.iot.ss.device.IBaseOnDeviceChangedListener listener) throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
try {
_data.writeInterfaceToken(DESCRIPTOR);
_data.writeStrongBinder((((listener!=null))?(listener.asBinder()):(null)));
mRemote.transact(Stub.TRANSACTION_addOnDeviceChangedListener, _data, _reply, 0);
_reply.readException();
}
finally {
_reply.recycle();
_data.recycle();
}
}
@Override public void removeOnDeviceChangedListener(swaiotos.channel.iot.ss.device.IBaseOnDeviceChangedListener listener) throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
try {
_data.writeInterfaceToken(DESCRIPTOR);
_data.writeStrongBinder((((listener!=null))?(listener.asBinder()):(null)));
mRemote.transact(Stub.TRANSACTION_removeOnDeviceChangedListener, _data, _reply, 0);
_reply.readException();
}
finally {
_reply.recycle();
_data.recycle();
}
}
@Override public void addDeviceBindListener(swaiotos.channel.iot.ss.device.IDeviceRelationListener listener) throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
try {
_data.writeInterfaceToken(DESCRIPTOR);
_data.writeStrongBinder((((listener!=null))?(listener.asBinder()):(null)));
mRemote.transact(Stub.TRANSACTION_addDeviceBindListener, _data, _reply, 0);
_reply.readException();
}
finally {
_reply.recycle();
_data.recycle();
}
}
@Override public void removeDeviceBindListener(swaiotos.channel.iot.ss.device.IDeviceRelationListener listener) throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
try {
_data.writeInterfaceToken(DESCRIPTOR);
_data.writeStrongBinder((((listener!=null))?(listener.asBinder()):(null)));
mRemote.transact(Stub.TRANSACTION_removeDeviceBindListener, _data, _reply, 0);
_reply.readException();
}
finally {
_reply.recycle();
_data.recycle();
}
}
@Override public void addDeviceInfoUpdateListener(swaiotos.channel.iot.ss.device.IBaseDeviceInfoUpdateListener listener) throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
try {
_data.writeInterfaceToken(DESCRIPTOR);
_data.writeStrongBinder((((listener!=null))?(listener.asBinder()):(null)));
mRemote.transact(Stub.TRANSACTION_addDeviceInfoUpdateListener, _data, _reply, 0);
_reply.readException();
}
finally {
_reply.recycle();
_data.recycle();
}
}
@Override public void removeDeviceInfoUpdateListener(swaiotos.channel.iot.ss.device.IBaseDeviceInfoUpdateListener listener) throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
try {
_data.writeInterfaceToken(DESCRIPTOR);
_data.writeStrongBinder((((listener!=null))?(listener.asBinder()):(null)));
mRemote.transact(Stub.TRANSACTION_removeDeviceInfoUpdateListener, _data, _reply, 0);
_reply.readException();
}
finally {
_reply.recycle();
_data.recycle();
}
}
@Override public void addDevicesReflushListener(swaiotos.channel.iot.ss.device.IBaseDevicesReflushListener listener) throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
try {
_data.writeInterfaceToken(DESCRIPTOR);
_data.writeStrongBinder((((listener!=null))?(listener.asBinder()):(null)));
mRemote.transact(Stub.TRANSACTION_addDevicesReflushListener, _data, _reply, 0);
_reply.readException();
}
finally {
_reply.recycle();
_data.recycle();
}
}
@Override public void removeDevicesReflushListener(swaiotos.channel.iot.ss.device.IBaseDevicesReflushListener listener) throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
try {
_data.writeInterfaceToken(DESCRIPTOR);
_data.writeStrongBinder((((listener!=null))?(listener.asBinder()):(null)));
mRemote.transact(Stub.TRANSACTION_removeDevicesReflushListener, _data, _reply, 0);
_reply.readException();
}
finally {
_reply.recycle();
_data.recycle();
}
}
@Override public java.util.List<swaiotos.channel.iot.ss.device.Device> updateDeviceList() throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
java.util.List<swaiotos.channel.iot.ss.device.Device> _result;
try {
_data.writeInterfaceToken(DESCRIPTOR);
mRemote.transact(Stub.TRANSACTION_updateDeviceList, _data, _reply, 0);
_reply.readException();
_result = _reply.createTypedArrayList(swaiotos.channel.iot.ss.device.Device.CREATOR);
}
finally {
_reply.recycle();
_data.recycle();
}
return _result;
}
@Override public java.lang.String getAccessToken() throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
java.lang.String _result;
try {
_data.writeInterfaceToken(DESCRIPTOR);
mRemote.transact(Stub.TRANSACTION_getAccessToken, _data, _reply, 0);
_reply.readException();
_result = _reply.readString();
}
finally {
_reply.recycle();
_data.recycle();
}
return _result;
}
}
static final int TRANSACTION_getDevices = (android.os.IBinder.FIRST_CALL_TRANSACTION + 0);
static final int TRANSACTION_getDeviceOnlineStatus = (android.os.IBinder.FIRST_CALL_TRANSACTION + 1);
static final int TRANSACTION_getCurrentDevice = (android.os.IBinder.FIRST_CALL_TRANSACTION + 2);
static final int TRANSACTION_addOnDeviceChangedListener = (android.os.IBinder.FIRST_CALL_TRANSACTION + 3);
static final int TRANSACTION_removeOnDeviceChangedListener = (android.os.IBinder.FIRST_CALL_TRANSACTION + 4);
static final int TRANSACTION_addDeviceBindListener = (android.os.IBinder.FIRST_CALL_TRANSACTION + 5);
static final int TRANSACTION_removeDeviceBindListener = (android.os.IBinder.FIRST_CALL_TRANSACTION + 6);
static final int TRANSACTION_addDeviceInfoUpdateListener = (android.os.IBinder.FIRST_CALL_TRANSACTION + 7);
static final int TRANSACTION_removeDeviceInfoUpdateListener = (android.os.IBinder.FIRST_CALL_TRANSACTION + 8);
static final int TRANSACTION_addDevicesReflushListener = (android.os.IBinder.FIRST_CALL_TRANSACTION + 9);
static final int TRANSACTION_removeDevicesReflushListener = (android.os.IBinder.FIRST_CALL_TRANSACTION + 10);
static final int TRANSACTION_updateDeviceList = (android.os.IBinder.FIRST_CALL_TRANSACTION + 11);
static final int TRANSACTION_getAccessToken = (android.os.IBinder.FIRST_CALL_TRANSACTION + 12);
}
public java.util.List<swaiotos.channel.iot.ss.device.Device> getDevices() throws android.os.RemoteException;
public java.util.List<swaiotos.channel.iot.ss.device.Device> getDeviceOnlineStatus() throws android.os.RemoteException;
public swaiotos.channel.iot.ss.device.Device getCurrentDevice() throws android.os.RemoteException;
public void addOnDeviceChangedListener(swaiotos.channel.iot.ss.device.IBaseOnDeviceChangedListener listener) throws android.os.RemoteException;
public void removeOnDeviceChangedListener(swaiotos.channel.iot.ss.device.IBaseOnDeviceChangedListener listener) throws android.os.RemoteException;
public void addDeviceBindListener(swaiotos.channel.iot.ss.device.IDeviceRelationListener listener) throws android.os.RemoteException;
public void removeDeviceBindListener(swaiotos.channel.iot.ss.device.IDeviceRelationListener listener) throws android.os.RemoteException;
public void addDeviceInfoUpdateListener(swaiotos.channel.iot.ss.device.IBaseDeviceInfoUpdateListener listener) throws android.os.RemoteException;
public void removeDeviceInfoUpdateListener(swaiotos.channel.iot.ss.device.IBaseDeviceInfoUpdateListener listener) throws android.os.RemoteException;
public void addDevicesReflushListener(swaiotos.channel.iot.ss.device.IBaseDevicesReflushListener listener) throws android.os.RemoteException;
public void removeDevicesReflushListener(swaiotos.channel.iot.ss.device.IBaseDevicesReflushListener listener) throws android.os.RemoteException;
public java.util.List<swaiotos.channel.iot.ss.device.Device> updateDeviceList() throws android.os.RemoteException;
public java.lang.String getAccessToken() throws android.os.RemoteException;
}
