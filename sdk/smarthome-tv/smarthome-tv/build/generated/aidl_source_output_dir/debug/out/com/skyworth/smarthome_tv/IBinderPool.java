/*
 * This file is auto-generated.  DO NOT MODIFY.
 */
package com.skyworth.smarthome_tv;
/**
 * Describe:
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/2/25
 */
public interface IBinderPool extends android.os.IInterface
{
  /** Default implementation for IBinderPool. */
  public static class Default implements com.skyworth.smarthome_tv.IBinderPool
  {
    @Override public android.os.IBinder queryBinder(java.lang.String action) throws android.os.RemoteException
    {
      return null;
    }
    @Override
    public android.os.IBinder asBinder() {
      return null;
    }
  }
  /** Local-side IPC implementation stub class. */
  public static abstract class Stub extends android.os.Binder implements com.skyworth.smarthome_tv.IBinderPool
  {
    private static final java.lang.String DESCRIPTOR = "com.skyworth.smarthome_tv.IBinderPool";
    /** Construct the stub at attach it to the interface. */
    public Stub()
    {
      this.attachInterface(this, DESCRIPTOR);
    }
    /**
     * Cast an IBinder object into an com.skyworth.smarthome_tv.IBinderPool interface,
     * generating a proxy if needed.
     */
    public static com.skyworth.smarthome_tv.IBinderPool asInterface(android.os.IBinder obj)
    {
      if ((obj==null)) {
        return null;
      }
      android.os.IInterface iin = obj.queryLocalInterface(DESCRIPTOR);
      if (((iin!=null)&&(iin instanceof com.skyworth.smarthome_tv.IBinderPool))) {
        return ((com.skyworth.smarthome_tv.IBinderPool)iin);
      }
      return new com.skyworth.smarthome_tv.IBinderPool.Stub.Proxy(obj);
    }
    @Override public android.os.IBinder asBinder()
    {
      return this;
    }
    @Override public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws android.os.RemoteException
    {
      java.lang.String descriptor = DESCRIPTOR;
      switch (code)
      {
        case INTERFACE_TRANSACTION:
        {
          reply.writeString(descriptor);
          return true;
        }
        case TRANSACTION_queryBinder:
        {
          data.enforceInterface(descriptor);
          java.lang.String _arg0;
          _arg0 = data.readString();
          android.os.IBinder _result = this.queryBinder(_arg0);
          reply.writeNoException();
          reply.writeStrongBinder(_result);
          return true;
        }
        default:
        {
          return super.onTransact(code, data, reply, flags);
        }
      }
    }
    private static class Proxy implements com.skyworth.smarthome_tv.IBinderPool
    {
      private android.os.IBinder mRemote;
      Proxy(android.os.IBinder remote)
      {
        mRemote = remote;
      }
      @Override public android.os.IBinder asBinder()
      {
        return mRemote;
      }
      public java.lang.String getInterfaceDescriptor()
      {
        return DESCRIPTOR;
      }
      @Override public android.os.IBinder queryBinder(java.lang.String action) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        android.os.IBinder _result;
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(action);
          boolean _status = mRemote.transact(Stub.TRANSACTION_queryBinder, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            return getDefaultImpl().queryBinder(action);
          }
          _reply.readException();
          _result = _reply.readStrongBinder();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
        return _result;
      }
      public static com.skyworth.smarthome_tv.IBinderPool sDefaultImpl;
    }
    static final int TRANSACTION_queryBinder = (android.os.IBinder.FIRST_CALL_TRANSACTION + 0);
    public static boolean setDefaultImpl(com.skyworth.smarthome_tv.IBinderPool impl) {
      if (Stub.Proxy.sDefaultImpl == null && impl != null) {
        Stub.Proxy.sDefaultImpl = impl;
        return true;
      }
      return false;
    }
    public static com.skyworth.smarthome_tv.IBinderPool getDefaultImpl() {
      return Stub.Proxy.sDefaultImpl;
    }
  }
  public android.os.IBinder queryBinder(java.lang.String action) throws android.os.RemoteException;
}
