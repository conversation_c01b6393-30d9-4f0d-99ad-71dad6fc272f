/*
 * This file is auto-generated.  DO NOT MODIFY.
 */
package com.skyworth.smarthome.third;
// Declare any non-default types here with import statements

public interface IThirdLaunch extends android.os.IInterface
{
  /** Default implementation for IThirdLaunch. */
  public static class Default implements com.skyworth.smarthome.third.IThirdLaunch
  {
    @Override public void init() throws android.os.RemoteException
    {
    }
    @Override public boolean start(java.lang.String params) throws android.os.RemoteException
    {
      return false;
    }
    @Override public void setCallback(com.skyworth.smarthome.third.IThirdLaunchHostCallback callback) throws android.os.RemoteException
    {
    }
    @Override public void onEvent(java.lang.String type, java.util.Map params) throws android.os.RemoteException
    {
    }
    @Override
    public android.os.IBinder asBinder() {
      return null;
    }
  }
  /** Local-side IPC implementation stub class. */
  public static abstract class Stub extends android.os.Binder implements com.skyworth.smarthome.third.IThirdLaunch
  {
    private static final java.lang.String DESCRIPTOR = "com.skyworth.smarthome.third.IThirdLaunch";
    /** Construct the stub at attach it to the interface. */
    public Stub()
    {
      this.attachInterface(this, DESCRIPTOR);
    }
    /**
     * Cast an IBinder object into an com.skyworth.smarthome.third.IThirdLaunch interface,
     * generating a proxy if needed.
     */
    public static com.skyworth.smarthome.third.IThirdLaunch asInterface(android.os.IBinder obj)
    {
      if ((obj==null)) {
        return null;
      }
      android.os.IInterface iin = obj.queryLocalInterface(DESCRIPTOR);
      if (((iin!=null)&&(iin instanceof com.skyworth.smarthome.third.IThirdLaunch))) {
        return ((com.skyworth.smarthome.third.IThirdLaunch)iin);
      }
      return new com.skyworth.smarthome.third.IThirdLaunch.Stub.Proxy(obj);
    }
    @Override public android.os.IBinder asBinder()
    {
      return this;
    }
    @Override public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws android.os.RemoteException
    {
      java.lang.String descriptor = DESCRIPTOR;
      switch (code)
      {
        case INTERFACE_TRANSACTION:
        {
          reply.writeString(descriptor);
          return true;
        }
        case TRANSACTION_init:
        {
          data.enforceInterface(descriptor);
          this.init();
          reply.writeNoException();
          return true;
        }
        case TRANSACTION_start:
        {
          data.enforceInterface(descriptor);
          java.lang.String _arg0;
          _arg0 = data.readString();
          boolean _result = this.start(_arg0);
          reply.writeNoException();
          reply.writeInt(((_result)?(1):(0)));
          return true;
        }
        case TRANSACTION_setCallback:
        {
          data.enforceInterface(descriptor);
          com.skyworth.smarthome.third.IThirdLaunchHostCallback _arg0;
          _arg0 = com.skyworth.smarthome.third.IThirdLaunchHostCallback.Stub.asInterface(data.readStrongBinder());
          this.setCallback(_arg0);
          reply.writeNoException();
          return true;
        }
        case TRANSACTION_onEvent:
        {
          data.enforceInterface(descriptor);
          java.lang.String _arg0;
          _arg0 = data.readString();
          java.util.Map _arg1;
          java.lang.ClassLoader cl = (java.lang.ClassLoader)this.getClass().getClassLoader();
          _arg1 = data.readHashMap(cl);
          this.onEvent(_arg0, _arg1);
          reply.writeNoException();
          return true;
        }
        default:
        {
          return super.onTransact(code, data, reply, flags);
        }
      }
    }
    private static class Proxy implements com.skyworth.smarthome.third.IThirdLaunch
    {
      private android.os.IBinder mRemote;
      Proxy(android.os.IBinder remote)
      {
        mRemote = remote;
      }
      @Override public android.os.IBinder asBinder()
      {
        return mRemote;
      }
      public java.lang.String getInterfaceDescriptor()
      {
        return DESCRIPTOR;
      }
      @Override public void init() throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          boolean _status = mRemote.transact(Stub.TRANSACTION_init, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().init();
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public boolean start(java.lang.String params) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        boolean _result;
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(params);
          boolean _status = mRemote.transact(Stub.TRANSACTION_start, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            return getDefaultImpl().start(params);
          }
          _reply.readException();
          _result = (0!=_reply.readInt());
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
        return _result;
      }
      @Override public void setCallback(com.skyworth.smarthome.third.IThirdLaunchHostCallback callback) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeStrongBinder((((callback!=null))?(callback.asBinder()):(null)));
          boolean _status = mRemote.transact(Stub.TRANSACTION_setCallback, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().setCallback(callback);
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public void onEvent(java.lang.String type, java.util.Map params) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(type);
          _data.writeMap(params);
          boolean _status = mRemote.transact(Stub.TRANSACTION_onEvent, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().onEvent(type, params);
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      public static com.skyworth.smarthome.third.IThirdLaunch sDefaultImpl;
    }
    static final int TRANSACTION_init = (android.os.IBinder.FIRST_CALL_TRANSACTION + 0);
    static final int TRANSACTION_start = (android.os.IBinder.FIRST_CALL_TRANSACTION + 1);
    static final int TRANSACTION_setCallback = (android.os.IBinder.FIRST_CALL_TRANSACTION + 2);
    static final int TRANSACTION_onEvent = (android.os.IBinder.FIRST_CALL_TRANSACTION + 3);
    public static boolean setDefaultImpl(com.skyworth.smarthome.third.IThirdLaunch impl) {
      if (Stub.Proxy.sDefaultImpl == null && impl != null) {
        Stub.Proxy.sDefaultImpl = impl;
        return true;
      }
      return false;
    }
    public static com.skyworth.smarthome.third.IThirdLaunch getDefaultImpl() {
      return Stub.Proxy.sDefaultImpl;
    }
  }
  public void init() throws android.os.RemoteException;
  public boolean start(java.lang.String params) throws android.os.RemoteException;
  public void setCallback(com.skyworth.smarthome.third.IThirdLaunchHostCallback callback) throws android.os.RemoteException;
  public void onEvent(java.lang.String type, java.util.Map params) throws android.os.RemoteException;
}
