package com.swaiot.aiotlib.service.binder.family;


import android.os.RemoteException;

import com.swaiot.aiotlib.common.model.AiotConstants;
import com.swaiot.aiotlib.common.util.EmptyUtils;
import com.swaiot.aiotlib.family.IFamilyManager;
import com.swaiot.lib.SkyAIOTContract;


/**
 * @ClassName: FamilyManagerBinder
 * @Author: AwenZeng
 * @CreateDate: 2020/5/8 11:30
 * @Description: 家庭相关binder
 */
public class FamilyManagerBinder extends IFamilyManager.Stub {

    @Override
    public void setCurrentFamily(String familyId, String commandId) throws RemoteException {
        SkyAIOTContract.require_resource(AiotConstants.KEY_HTTP_DEVCIE_LIST,  EmptyUtils.handleNullString(commandId),(byte)0);
        SkyAIOTContract.control_object(AiotConstants.KEY_HTTP_SET_CURRENT_FAMILY,  EmptyUtils.handleNullString(familyId), "",  EmptyUtils.handleNullString(commandId));
    }

    @Override
    public void addFamily(String commandId) throws RemoteException {

    }

    @Override
    public void editFamily(String familyId, String commandId) throws RemoteException {

    }

    @Override
    public void deleteFamily(String familyId, String commandId) throws RemoteException {

    }
}
