1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="swaiotos.channel.iot.sdk"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="15"
8-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/sdk/iot-channel-sdk/sdk/src/main/AndroidManifest.xml
9        android:targetSdkVersion="28" />
9-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/sdk/iot-channel-sdk/sdk/src/main/AndroidManifest.xml
10
11    <uses-permission android:name="android.permission.BLUETOOTH" />
11-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/sdk/iot-channel-sdk/sdk/src/main/AndroidManifest.xml:4:5-68
11-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/sdk/iot-channel-sdk/sdk/src/main/AndroidManifest.xml:4:22-65
12    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
12-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/sdk/iot-channel-sdk/sdk/src/main/AndroidManifest.xml:5:5-74
12-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/sdk/iot-channel-sdk/sdk/src/main/AndroidManifest.xml:5:22-71
13    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
13-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/sdk/iot-channel-sdk/sdk/src/main/AndroidManifest.xml:6:5-76
13-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/sdk/smarthome-aiot/sdk/iot-channel-sdk/sdk/src/main/AndroidManifest.xml:6:22-74
14
15</manifest>
