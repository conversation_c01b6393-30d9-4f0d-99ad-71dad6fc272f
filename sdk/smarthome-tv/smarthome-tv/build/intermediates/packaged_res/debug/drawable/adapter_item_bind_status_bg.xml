<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_selected="true">
        <shape>
            <corners android:radius="500dp" />
            <solid android:color="@color/translucent" />
            <stroke android:color="#FF000000" android:width="1dp"/>
        </shape>
    </item>
    <item android:state_selected="false">
        <shape>
            <corners android:radius="500dp" />
            <solid android:color="#19CCCCCC" />
            <stroke android:color="#80CCCCCC" android:width="1dp"/>
        </shape>
    </item>
</selector>