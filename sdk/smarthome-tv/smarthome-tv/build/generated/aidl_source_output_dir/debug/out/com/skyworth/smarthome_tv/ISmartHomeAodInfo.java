/*
 * This file is auto-generated.  DO NOT MODIFY.
 */
package com.skyworth.smarthome_tv;
/**
 * Example of a secondary interface associated with a service.  (Note that
 * the interface itself doesn't impact, it is just a matter of how you
 * retrieve it from the service.)
 */
public interface ISmartHomeAodInfo extends android.os.IInterface
{
  /** Default implementation for ISmartHomeAodInfo. */
  public static class Default implements com.skyworth.smarthome_tv.ISmartHomeAodInfo
  {
    @Override public java.lang.String getDeviceList() throws android.os.RemoteException
    {
      return null;
    }
    @Override public void controlDevice(java.lang.String status, java.lang.String deviceID) throws android.os.RemoteException
    {
    }
    @Override public void goToSmartHome(java.lang.String type, java.lang.String param) throws android.os.RemoteException
    {
    }
    @Override
    public android.os.IBinder asBinder() {
      return null;
    }
  }
  /** Local-side IPC implementation stub class. */
  public static abstract class Stub extends android.os.Binder implements com.skyworth.smarthome_tv.ISmartHomeAodInfo
  {
    private static final java.lang.String DESCRIPTOR = "com.skyworth.smarthome_tv.ISmartHomeAodInfo";
    /** Construct the stub at attach it to the interface. */
    public Stub()
    {
      this.attachInterface(this, DESCRIPTOR);
    }
    /**
     * Cast an IBinder object into an com.skyworth.smarthome_tv.ISmartHomeAodInfo interface,
     * generating a proxy if needed.
     */
    public static com.skyworth.smarthome_tv.ISmartHomeAodInfo asInterface(android.os.IBinder obj)
    {
      if ((obj==null)) {
        return null;
      }
      android.os.IInterface iin = obj.queryLocalInterface(DESCRIPTOR);
      if (((iin!=null)&&(iin instanceof com.skyworth.smarthome_tv.ISmartHomeAodInfo))) {
        return ((com.skyworth.smarthome_tv.ISmartHomeAodInfo)iin);
      }
      return new com.skyworth.smarthome_tv.ISmartHomeAodInfo.Stub.Proxy(obj);
    }
    @Override public android.os.IBinder asBinder()
    {
      return this;
    }
    @Override public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws android.os.RemoteException
    {
      java.lang.String descriptor = DESCRIPTOR;
      switch (code)
      {
        case INTERFACE_TRANSACTION:
        {
          reply.writeString(descriptor);
          return true;
        }
        case TRANSACTION_getDeviceList:
        {
          data.enforceInterface(descriptor);
          java.lang.String _result = this.getDeviceList();
          reply.writeNoException();
          reply.writeString(_result);
          return true;
        }
        case TRANSACTION_controlDevice:
        {
          data.enforceInterface(descriptor);
          java.lang.String _arg0;
          _arg0 = data.readString();
          java.lang.String _arg1;
          _arg1 = data.readString();
          this.controlDevice(_arg0, _arg1);
          reply.writeNoException();
          return true;
        }
        case TRANSACTION_goToSmartHome:
        {
          data.enforceInterface(descriptor);
          java.lang.String _arg0;
          _arg0 = data.readString();
          java.lang.String _arg1;
          _arg1 = data.readString();
          this.goToSmartHome(_arg0, _arg1);
          reply.writeNoException();
          return true;
        }
        default:
        {
          return super.onTransact(code, data, reply, flags);
        }
      }
    }
    private static class Proxy implements com.skyworth.smarthome_tv.ISmartHomeAodInfo
    {
      private android.os.IBinder mRemote;
      Proxy(android.os.IBinder remote)
      {
        mRemote = remote;
      }
      @Override public android.os.IBinder asBinder()
      {
        return mRemote;
      }
      public java.lang.String getInterfaceDescriptor()
      {
        return DESCRIPTOR;
      }
      @Override public java.lang.String getDeviceList() throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        java.lang.String _result;
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          boolean _status = mRemote.transact(Stub.TRANSACTION_getDeviceList, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            return getDefaultImpl().getDeviceList();
          }
          _reply.readException();
          _result = _reply.readString();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
        return _result;
      }
      @Override public void controlDevice(java.lang.String status, java.lang.String deviceID) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(status);
          _data.writeString(deviceID);
          boolean _status = mRemote.transact(Stub.TRANSACTION_controlDevice, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().controlDevice(status, deviceID);
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public void goToSmartHome(java.lang.String type, java.lang.String param) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(type);
          _data.writeString(param);
          boolean _status = mRemote.transact(Stub.TRANSACTION_goToSmartHome, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().goToSmartHome(type, param);
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      public static com.skyworth.smarthome_tv.ISmartHomeAodInfo sDefaultImpl;
    }
    static final int TRANSACTION_getDeviceList = (android.os.IBinder.FIRST_CALL_TRANSACTION + 0);
    static final int TRANSACTION_controlDevice = (android.os.IBinder.FIRST_CALL_TRANSACTION + 1);
    static final int TRANSACTION_goToSmartHome = (android.os.IBinder.FIRST_CALL_TRANSACTION + 2);
    public static boolean setDefaultImpl(com.skyworth.smarthome_tv.ISmartHomeAodInfo impl) {
      if (Stub.Proxy.sDefaultImpl == null && impl != null) {
        Stub.Proxy.sDefaultImpl = impl;
        return true;
      }
      return false;
    }
    public static com.skyworth.smarthome_tv.ISmartHomeAodInfo getDefaultImpl() {
      return Stub.Proxy.sDefaultImpl;
    }
  }
  public java.lang.String getDeviceList() throws android.os.RemoteException;
  public void controlDevice(java.lang.String status, java.lang.String deviceID) throws android.os.RemoteException;
  public void goToSmartHome(java.lang.String type, java.lang.String param) throws android.os.RemoteException;
}
