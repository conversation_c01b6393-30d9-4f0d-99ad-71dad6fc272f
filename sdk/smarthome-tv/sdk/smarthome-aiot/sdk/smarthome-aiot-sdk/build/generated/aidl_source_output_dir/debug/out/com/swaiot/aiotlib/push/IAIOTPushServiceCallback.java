/*
 * This file is auto-generated.  DO NOT MODIFY.
 */
package com.swaiot.aiotlib.push;
// Declare any non-default types here with import statements

public interface IAIOTPushServiceCallback extends android.os.IInterface
{
  /** Default implementation for IAIOTPushServiceCallback. */
  public static class Default implements com.swaiot.aiotlib.push.IAIOTPushServiceCallback
  {
    @Override public void onInit(java.lang.String data) throws android.os.RemoteException
    {
    }
    @Override public void onAccountChange() throws android.os.RemoteException
    {
    }
    @Override public void onReceiveData(java.lang.String event, java.lang.String data) throws android.os.RemoteException
    {
    }
    @Override public void onHandleDataCallback(java.lang.String object_type, java.lang.String device_id, java.lang.String data) throws android.os.RemoteException
    {
    }
    @Override public void onHttpCallBack(int code, java.lang.String msg, java.lang.String data, java.lang.String resource_type, java.lang.String commandId) throws android.os.RemoteException
    {
    }
    @Override public void onApconfigProgress(int progress, int total, java.lang.String extra) throws android.os.RemoteException
    {
    }
    @Override public void onApconfigOk(java.lang.String result) throws android.os.RemoteException
    {
    }
    @Override public void onApconfigFail(int code, java.lang.String erro) throws android.os.RemoteException
    {
    }
    @Override public void onApconfigConnectNetFail(java.lang.String wifiInfo) throws android.os.RemoteException
    {
    }
    @Override public void onDiscoverWifiDevice(java.lang.String result) throws android.os.RemoteException
    {
    }
    @Override public void onDiscoverNetworkDevice(java.lang.String result) throws android.os.RemoteException
    {
    }
    @Override public void onSpecialVoiceHandle(java.lang.String cmd) throws android.os.RemoteException
    {
    }
    @Override
    public android.os.IBinder asBinder() {
      return null;
    }
  }
  /** Local-side IPC implementation stub class. */
  public static abstract class Stub extends android.os.Binder implements com.swaiot.aiotlib.push.IAIOTPushServiceCallback
  {
    private static final java.lang.String DESCRIPTOR = "com.swaiot.aiotlib.push.IAIOTPushServiceCallback";
    /** Construct the stub at attach it to the interface. */
    public Stub()
    {
      this.attachInterface(this, DESCRIPTOR);
    }
    /**
     * Cast an IBinder object into an com.swaiot.aiotlib.push.IAIOTPushServiceCallback interface,
     * generating a proxy if needed.
     */
    public static com.swaiot.aiotlib.push.IAIOTPushServiceCallback asInterface(android.os.IBinder obj)
    {
      if ((obj==null)) {
        return null;
      }
      android.os.IInterface iin = obj.queryLocalInterface(DESCRIPTOR);
      if (((iin!=null)&&(iin instanceof com.swaiot.aiotlib.push.IAIOTPushServiceCallback))) {
        return ((com.swaiot.aiotlib.push.IAIOTPushServiceCallback)iin);
      }
      return new com.swaiot.aiotlib.push.IAIOTPushServiceCallback.Stub.Proxy(obj);
    }
    @Override public android.os.IBinder asBinder()
    {
      return this;
    }
    @Override public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws android.os.RemoteException
    {
      java.lang.String descriptor = DESCRIPTOR;
      switch (code)
      {
        case INTERFACE_TRANSACTION:
        {
          reply.writeString(descriptor);
          return true;
        }
        case TRANSACTION_onInit:
        {
          data.enforceInterface(descriptor);
          java.lang.String _arg0;
          _arg0 = data.readString();
          this.onInit(_arg0);
          reply.writeNoException();
          return true;
        }
        case TRANSACTION_onAccountChange:
        {
          data.enforceInterface(descriptor);
          this.onAccountChange();
          reply.writeNoException();
          return true;
        }
        case TRANSACTION_onReceiveData:
        {
          data.enforceInterface(descriptor);
          java.lang.String _arg0;
          _arg0 = data.readString();
          java.lang.String _arg1;
          _arg1 = data.readString();
          this.onReceiveData(_arg0, _arg1);
          reply.writeNoException();
          return true;
        }
        case TRANSACTION_onHandleDataCallback:
        {
          data.enforceInterface(descriptor);
          java.lang.String _arg0;
          _arg0 = data.readString();
          java.lang.String _arg1;
          _arg1 = data.readString();
          java.lang.String _arg2;
          _arg2 = data.readString();
          this.onHandleDataCallback(_arg0, _arg1, _arg2);
          reply.writeNoException();
          return true;
        }
        case TRANSACTION_onHttpCallBack:
        {
          data.enforceInterface(descriptor);
          int _arg0;
          _arg0 = data.readInt();
          java.lang.String _arg1;
          _arg1 = data.readString();
          java.lang.String _arg2;
          _arg2 = data.readString();
          java.lang.String _arg3;
          _arg3 = data.readString();
          java.lang.String _arg4;
          _arg4 = data.readString();
          this.onHttpCallBack(_arg0, _arg1, _arg2, _arg3, _arg4);
          reply.writeNoException();
          return true;
        }
        case TRANSACTION_onApconfigProgress:
        {
          data.enforceInterface(descriptor);
          int _arg0;
          _arg0 = data.readInt();
          int _arg1;
          _arg1 = data.readInt();
          java.lang.String _arg2;
          _arg2 = data.readString();
          this.onApconfigProgress(_arg0, _arg1, _arg2);
          reply.writeNoException();
          return true;
        }
        case TRANSACTION_onApconfigOk:
        {
          data.enforceInterface(descriptor);
          java.lang.String _arg0;
          _arg0 = data.readString();
          this.onApconfigOk(_arg0);
          reply.writeNoException();
          return true;
        }
        case TRANSACTION_onApconfigFail:
        {
          data.enforceInterface(descriptor);
          int _arg0;
          _arg0 = data.readInt();
          java.lang.String _arg1;
          _arg1 = data.readString();
          this.onApconfigFail(_arg0, _arg1);
          reply.writeNoException();
          return true;
        }
        case TRANSACTION_onApconfigConnectNetFail:
        {
          data.enforceInterface(descriptor);
          java.lang.String _arg0;
          _arg0 = data.readString();
          this.onApconfigConnectNetFail(_arg0);
          reply.writeNoException();
          return true;
        }
        case TRANSACTION_onDiscoverWifiDevice:
        {
          data.enforceInterface(descriptor);
          java.lang.String _arg0;
          _arg0 = data.readString();
          this.onDiscoverWifiDevice(_arg0);
          reply.writeNoException();
          return true;
        }
        case TRANSACTION_onDiscoverNetworkDevice:
        {
          data.enforceInterface(descriptor);
          java.lang.String _arg0;
          _arg0 = data.readString();
          this.onDiscoverNetworkDevice(_arg0);
          reply.writeNoException();
          return true;
        }
        case TRANSACTION_onSpecialVoiceHandle:
        {
          data.enforceInterface(descriptor);
          java.lang.String _arg0;
          _arg0 = data.readString();
          this.onSpecialVoiceHandle(_arg0);
          reply.writeNoException();
          return true;
        }
        default:
        {
          return super.onTransact(code, data, reply, flags);
        }
      }
    }
    private static class Proxy implements com.swaiot.aiotlib.push.IAIOTPushServiceCallback
    {
      private android.os.IBinder mRemote;
      Proxy(android.os.IBinder remote)
      {
        mRemote = remote;
      }
      @Override public android.os.IBinder asBinder()
      {
        return mRemote;
      }
      public java.lang.String getInterfaceDescriptor()
      {
        return DESCRIPTOR;
      }
      @Override public void onInit(java.lang.String data) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(data);
          boolean _status = mRemote.transact(Stub.TRANSACTION_onInit, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().onInit(data);
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public void onAccountChange() throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          boolean _status = mRemote.transact(Stub.TRANSACTION_onAccountChange, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().onAccountChange();
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public void onReceiveData(java.lang.String event, java.lang.String data) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(event);
          _data.writeString(data);
          boolean _status = mRemote.transact(Stub.TRANSACTION_onReceiveData, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().onReceiveData(event, data);
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public void onHandleDataCallback(java.lang.String object_type, java.lang.String device_id, java.lang.String data) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(object_type);
          _data.writeString(device_id);
          _data.writeString(data);
          boolean _status = mRemote.transact(Stub.TRANSACTION_onHandleDataCallback, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().onHandleDataCallback(object_type, device_id, data);
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public void onHttpCallBack(int code, java.lang.String msg, java.lang.String data, java.lang.String resource_type, java.lang.String commandId) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeInt(code);
          _data.writeString(msg);
          _data.writeString(data);
          _data.writeString(resource_type);
          _data.writeString(commandId);
          boolean _status = mRemote.transact(Stub.TRANSACTION_onHttpCallBack, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().onHttpCallBack(code, msg, data, resource_type, commandId);
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public void onApconfigProgress(int progress, int total, java.lang.String extra) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeInt(progress);
          _data.writeInt(total);
          _data.writeString(extra);
          boolean _status = mRemote.transact(Stub.TRANSACTION_onApconfigProgress, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().onApconfigProgress(progress, total, extra);
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public void onApconfigOk(java.lang.String result) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(result);
          boolean _status = mRemote.transact(Stub.TRANSACTION_onApconfigOk, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().onApconfigOk(result);
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public void onApconfigFail(int code, java.lang.String erro) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeInt(code);
          _data.writeString(erro);
          boolean _status = mRemote.transact(Stub.TRANSACTION_onApconfigFail, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().onApconfigFail(code, erro);
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public void onApconfigConnectNetFail(java.lang.String wifiInfo) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(wifiInfo);
          boolean _status = mRemote.transact(Stub.TRANSACTION_onApconfigConnectNetFail, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().onApconfigConnectNetFail(wifiInfo);
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public void onDiscoverWifiDevice(java.lang.String result) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(result);
          boolean _status = mRemote.transact(Stub.TRANSACTION_onDiscoverWifiDevice, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().onDiscoverWifiDevice(result);
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public void onDiscoverNetworkDevice(java.lang.String result) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(result);
          boolean _status = mRemote.transact(Stub.TRANSACTION_onDiscoverNetworkDevice, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().onDiscoverNetworkDevice(result);
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public void onSpecialVoiceHandle(java.lang.String cmd) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(cmd);
          boolean _status = mRemote.transact(Stub.TRANSACTION_onSpecialVoiceHandle, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().onSpecialVoiceHandle(cmd);
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      public static com.swaiot.aiotlib.push.IAIOTPushServiceCallback sDefaultImpl;
    }
    static final int TRANSACTION_onInit = (android.os.IBinder.FIRST_CALL_TRANSACTION + 0);
    static final int TRANSACTION_onAccountChange = (android.os.IBinder.FIRST_CALL_TRANSACTION + 1);
    static final int TRANSACTION_onReceiveData = (android.os.IBinder.FIRST_CALL_TRANSACTION + 2);
    static final int TRANSACTION_onHandleDataCallback = (android.os.IBinder.FIRST_CALL_TRANSACTION + 3);
    static final int TRANSACTION_onHttpCallBack = (android.os.IBinder.FIRST_CALL_TRANSACTION + 4);
    static final int TRANSACTION_onApconfigProgress = (android.os.IBinder.FIRST_CALL_TRANSACTION + 5);
    static final int TRANSACTION_onApconfigOk = (android.os.IBinder.FIRST_CALL_TRANSACTION + 6);
    static final int TRANSACTION_onApconfigFail = (android.os.IBinder.FIRST_CALL_TRANSACTION + 7);
    static final int TRANSACTION_onApconfigConnectNetFail = (android.os.IBinder.FIRST_CALL_TRANSACTION + 8);
    static final int TRANSACTION_onDiscoverWifiDevice = (android.os.IBinder.FIRST_CALL_TRANSACTION + 9);
    static final int TRANSACTION_onDiscoverNetworkDevice = (android.os.IBinder.FIRST_CALL_TRANSACTION + 10);
    static final int TRANSACTION_onSpecialVoiceHandle = (android.os.IBinder.FIRST_CALL_TRANSACTION + 11);
    public static boolean setDefaultImpl(com.swaiot.aiotlib.push.IAIOTPushServiceCallback impl) {
      if (Stub.Proxy.sDefaultImpl == null && impl != null) {
        Stub.Proxy.sDefaultImpl = impl;
        return true;
      }
      return false;
    }
    public static com.swaiot.aiotlib.push.IAIOTPushServiceCallback getDefaultImpl() {
      return Stub.Proxy.sDefaultImpl;
    }
  }
  public void onInit(java.lang.String data) throws android.os.RemoteException;
  public void onAccountChange() throws android.os.RemoteException;
  public void onReceiveData(java.lang.String event, java.lang.String data) throws android.os.RemoteException;
  public void onHandleDataCallback(java.lang.String object_type, java.lang.String device_id, java.lang.String data) throws android.os.RemoteException;
  public void onHttpCallBack(int code, java.lang.String msg, java.lang.String data, java.lang.String resource_type, java.lang.String commandId) throws android.os.RemoteException;
  public void onApconfigProgress(int progress, int total, java.lang.String extra) throws android.os.RemoteException;
  public void onApconfigOk(java.lang.String result) throws android.os.RemoteException;
  public void onApconfigFail(int code, java.lang.String erro) throws android.os.RemoteException;
  public void onApconfigConnectNetFail(java.lang.String wifiInfo) throws android.os.RemoteException;
  public void onDiscoverWifiDevice(java.lang.String result) throws android.os.RemoteException;
  public void onDiscoverNetworkDevice(java.lang.String result) throws android.os.RemoteException;
  public void onSpecialVoiceHandle(java.lang.String cmd) throws android.os.RemoteException;
}
