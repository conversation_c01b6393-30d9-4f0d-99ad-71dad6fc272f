1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.skyworth.smarthome"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="17"
8-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml
9        android:targetSdkVersion="29" />
9-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml
10
11    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" /> <!-- 获取网络状态改变的权限 -->
11-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:4:5-76
11-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:4:22-73
12    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" /> <!-- 允许应用程序改变网络状态 -->
12-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:5:5-79
12-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:5:22-76
13    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" /> <!-- 允许应用程序改变WIFI连接状态 -->
13-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:6:5-76
13-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:6:22-73
14    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
14-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:7:5-79
14-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:7:22-76
15    <uses-permission android:name="android.permission.INTERNET" /> <!-- 允许应用程序完全使用网络 -->
15-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:8:5-67
15-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:8:22-64
16    <uses-permission android:name="android.permission.BLUETOOTH" /> <!-- 获取蓝牙的权限 -->
16-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:9:5-68
16-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:9:22-65
17    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
17-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:10:5-81
17-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:10:22-78
18    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
18-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:11:5-81
18-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:11:22-78
19    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
19-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:12:5-75
19-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:12:22-72
20    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
20-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:13:5-80
20-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:13:22-77
21    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
21-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:14:5-78
21-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:14:22-75
22    <uses-permission android:name="android.permission.SYSTEM_OVERLAY_WINDOW" />
22-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:15:5-80
22-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:15:22-77
23    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
23-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:16:5-81
23-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:16:22-78
24    <uses-permission android:name="android.permission.BROADCAST_STICKY" />
24-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:17:5-75
24-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:17:22-72
25    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
25-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:18:5-79
25-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:18:22-76
26    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- 开启/禁用屏保权限 -->
26-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:19:5-68
26-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:19:22-65
27    <uses-permission android:name="com.tianci.user.permission.READ_CONTENT" />
27-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:20:5-79
27-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:20:22-76
28
29    <application>
29-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:21:5-128:19
30        <activity
30-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:22:9-30:20
31            android:name="com.skyworth.smarthome.personal.PersonalCenterActivity"
31-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:23:13-82
32            android:launchMode="singleTask"
32-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:25:13-44
33            android:screenOrientation="landscape" >
33-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:24:13-50
34            <intent-filter>
34-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:26:13-29:29
35                <action android:name="com.smarthome.action.PERSONAL_CENTER" />
35-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:27:17-79
35-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:27:25-76
36
37                <category android:name="android.intent.category.DEFAULT" />
37-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:28:17-76
37-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:28:27-73
38            </intent-filter>
39        </activity>
40        <activity
40-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:32:9-40:20
41            android:name="com.skyworth.smarthome.personal.unbinddevice.UnbindDeviceActivity"
41-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:33:13-93
42            android:launchMode="singleTask"
42-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:35:13-44
43            android:screenOrientation="landscape" >
43-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:34:13-50
44            <intent-filter>
44-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:36:13-39:29
45                <action android:name="com.smarthome.action.UNBIND_DEVICE" />
45-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:37:17-77
45-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:37:25-74
46
47                <category android:name="android.intent.category.DEFAULT" />
47-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:28:17-76
47-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:28:27-73
48            </intent-filter>
49        </activity>
50        <activity
50-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:42:9-50:20
51            android:name="com.skyworth.smarthome.personal.thirdaccount.ThirdAccountActivity"
51-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:43:13-93
52            android:launchMode="singleTask"
52-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:45:13-44
53            android:screenOrientation="landscape" >
53-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:44:13-50
54            <intent-filter>
54-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:46:13-49:29
55                <action android:name="com.smarthome.action.THRID_ACCOUNT" />
55-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:47:17-77
55-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:47:25-74
56
57                <category android:name="android.intent.category.DEFAULT" />
57-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:28:17-76
57-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:28:27-73
58            </intent-filter>
59        </activity>
60        <activity
60-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:52:9-60:20
61            android:name="com.skyworth.smarthome.home.DeviceGuideAcitvity"
61-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:53:13-75
62            android:launchMode="singleTask"
62-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:55:13-44
63            android:screenOrientation="landscape" >
63-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:54:13-50
64            <intent-filter>
64-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:56:13-59:29
65                <action android:name="com.smarthome.action.DEVICE_GUIDE" />
65-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:57:17-76
65-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:57:25-73
66
67                <category android:name="android.intent.category.DEFAULT" />
67-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:28:17-76
67-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:28:27-73
68            </intent-filter>
69        </activity>
70
71        <service
71-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:62:9-68:19
72            android:name="com.skyworth.smarthome.devices.apconfig.ApConfigService"
72-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:63:13-83
73            android:exported="false" >
73-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:64:13-37
74            <intent-filter>
74-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:65:13-67:29
75                <action android:name="coocaa.intent.action.APCONFIG_SERVICE" />
75-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:66:17-80
75-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:66:25-77
76            </intent-filter>
77        </service>
78        <service
78-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:70:9-80:19
79            android:name="com.skyworth.smarthome.service.SmartHomeService"
79-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:71:13-75
80            android:enabled="true"
80-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:72:13-35
81            android:exported="true" >
81-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:73:13-36
82            <intent-filter>
82-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:74:13-79:29
83                <action android:name="com.skyworth.aiot.ConnectBinderPool" />
83-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:75:17-78
83-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:75:25-75
84                <action android:name="com.skyworth.smarthome_tv.SmartHomeService" />
84-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:76:17-85
84-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:76:25-82
85                <action android:name="com.skyworth.smarthome_tv.ISmartHomePushService" />
85-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:77:17-90
85-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:77:25-87
86                <action android:name="com.skyworth.smarthome_tv.ISmartHomeService" />
86-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:78:17-86
86-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:78:25-83
87            </intent-filter>
88        </service>
89
90        <receiver
90-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:82:9-90:20
91            android:name="com.skyworth.smarthome.account.AccountReceiver"
91-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:83:13-74
92            android:enabled="true"
92-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:84:13-35
93            android:exported="true" >
93-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:85:13-36
94            <intent-filter>
94-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:86:13-89:29
95
96                <!-- 开机广播 -->
97                <action android:name="com.tianci.user.account_changed" />
97-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:88:17-74
97-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:88:25-71
98            </intent-filter>
99        </receiver>
100        <receiver
100-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:92:9-109:20
101            android:name="com.skyworth.smarthome.devices.discover.receiver.StartServiceReceiver"
101-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:93:13-97
102            android:enabled="true"
102-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:94:13-35
103            android:exported="true" >
103-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:95:13-36
104            <intent-filter>
104-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:96:13-108:29
105
106                <!-- 开机广播 -->
107                <action android:name="android.intent.action.BOOT_COMPLETED" />
107-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:98:17-79
107-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:98:25-76
108                <action android:name="com.skyworth.broadcast.standby.quick.resume" />
108-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:99:17-86
108-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:99:25-83
109                <action android:name="android.intent.action.BOOT_COMPLETED.TC" />
109-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:100:17-82
109-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:100:25-79
110                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
110-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:101:17-79
110-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:101:25-76
111                <action android:name="com.skyworth.broadcast.screensound.open" />
111-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:102:17-82
111-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:102:25-79
112                <action android:name="com.skyworth.broadcast.screensound.close" />
112-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:103:17-83
112-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:103:25-80
113                <action android:name="com.tianci.ad.start_screensaver" />
113-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:104:17-74
113-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:104:25-71
114                <action android:name="com.tianci.ad.exist_screensaver" />
114-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:105:17-74
114-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:105:25-71
115                <action android:name="com.skyworth.screensaverexit" />
115-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:106:17-71
115-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:106:25-68
116                <action android:name="com.skyworth.broadcast.recovery.start" />
116-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:107:17-80
116-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:107:25-77
117            </intent-filter>
118        </receiver>
119        <receiver android:name="com.skyworth.smarthome.voicehandle.VoiceHandleReceiver" >
119-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:111:9-117:20
119-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:111:19-88
120            <intent-filter>
120-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:112:13-116:29
121                <action android:name="com.skyworth.srtnj.action.voice.outcmd" />
121-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:113:17-81
121-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:113:25-78
122                <action android:name="com.skyworth.smarthome_tv.voicehandler" />
122-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:114:17-81
122-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:114:25-78
123                <action android:name="com.skyworth.smarthome_tv.helphandler" />
123-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:115:17-80
123-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:115:25-77
124            </intent-filter>
125        </receiver>
126
127        <meta-data
128            android:name="DEVICE_SERVER"
128-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:120:13-41
129            android:value="https://api-sit.skyworthiot.com/" />
129-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:121:13-51
130        <meta-data
131            android:name="HOMEPAGE_SERVER"
131-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:123:13-43
132            android:value="http://beta-api-home.skysrt.com/" />
132-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:124:13-53
133        <meta-data
134            android:name="APPSTORE_SERVER"
134-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:126:13-43
135            android:value="http://beta-tc.skysrt.com/appstore/appstorev3/" />
135-->/Users/<USER>/iot/NewTV_SmartHome/sdk/smarthome-tv/smarthome-tv/src/main/AndroidManifest.xml:127:13-53
136    </application>
137
138</manifest>
