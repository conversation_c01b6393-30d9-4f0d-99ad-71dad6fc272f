/*
 * This file is auto-generated.  DO NOT MODIFY.
 */
package com.swaiot.aiotlib.account;
// Declare any non-default types here with import statements

public interface IAccountInfo extends android.os.IInterface
{
  /** Default implementation for IAccountInfo. */
  public static class Default implements com.swaiot.aiotlib.account.IAccountInfo
  {
    @Override public void onInitAccountInfo(java.lang.String accountInfo) throws android.os.RemoteException
    {
    }
    @Override public void onAccountChange(boolean isLogin, java.lang.String accountInfo) throws android.os.RemoteException
    {
    }
    @Override
    public android.os.IBinder asBinder() {
      return null;
    }
  }
  /** Local-side IPC implementation stub class. */
  public static abstract class Stub extends android.os.Binder implements com.swaiot.aiotlib.account.IAccountInfo
  {
    private static final java.lang.String DESCRIPTOR = "com.swaiot.aiotlib.account.IAccountInfo";
    /** Construct the stub at attach it to the interface. */
    public Stub()
    {
      this.attachInterface(this, DESCRIPTOR);
    }
    /**
     * Cast an IBinder object into an com.swaiot.aiotlib.account.IAccountInfo interface,
     * generating a proxy if needed.
     */
    public static com.swaiot.aiotlib.account.IAccountInfo asInterface(android.os.IBinder obj)
    {
      if ((obj==null)) {
        return null;
      }
      android.os.IInterface iin = obj.queryLocalInterface(DESCRIPTOR);
      if (((iin!=null)&&(iin instanceof com.swaiot.aiotlib.account.IAccountInfo))) {
        return ((com.swaiot.aiotlib.account.IAccountInfo)iin);
      }
      return new com.swaiot.aiotlib.account.IAccountInfo.Stub.Proxy(obj);
    }
    @Override public android.os.IBinder asBinder()
    {
      return this;
    }
    @Override public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws android.os.RemoteException
    {
      java.lang.String descriptor = DESCRIPTOR;
      switch (code)
      {
        case INTERFACE_TRANSACTION:
        {
          reply.writeString(descriptor);
          return true;
        }
        case TRANSACTION_onInitAccountInfo:
        {
          data.enforceInterface(descriptor);
          java.lang.String _arg0;
          _arg0 = data.readString();
          this.onInitAccountInfo(_arg0);
          reply.writeNoException();
          return true;
        }
        case TRANSACTION_onAccountChange:
        {
          data.enforceInterface(descriptor);
          boolean _arg0;
          _arg0 = (0!=data.readInt());
          java.lang.String _arg1;
          _arg1 = data.readString();
          this.onAccountChange(_arg0, _arg1);
          reply.writeNoException();
          return true;
        }
        default:
        {
          return super.onTransact(code, data, reply, flags);
        }
      }
    }
    private static class Proxy implements com.swaiot.aiotlib.account.IAccountInfo
    {
      private android.os.IBinder mRemote;
      Proxy(android.os.IBinder remote)
      {
        mRemote = remote;
      }
      @Override public android.os.IBinder asBinder()
      {
        return mRemote;
      }
      public java.lang.String getInterfaceDescriptor()
      {
        return DESCRIPTOR;
      }
      @Override public void onInitAccountInfo(java.lang.String accountInfo) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(accountInfo);
          boolean _status = mRemote.transact(Stub.TRANSACTION_onInitAccountInfo, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().onInitAccountInfo(accountInfo);
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public void onAccountChange(boolean isLogin, java.lang.String accountInfo) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeInt(((isLogin)?(1):(0)));
          _data.writeString(accountInfo);
          boolean _status = mRemote.transact(Stub.TRANSACTION_onAccountChange, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().onAccountChange(isLogin, accountInfo);
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      public static com.swaiot.aiotlib.account.IAccountInfo sDefaultImpl;
    }
    static final int TRANSACTION_onInitAccountInfo = (android.os.IBinder.FIRST_CALL_TRANSACTION + 0);
    static final int TRANSACTION_onAccountChange = (android.os.IBinder.FIRST_CALL_TRANSACTION + 1);
    public static boolean setDefaultImpl(com.swaiot.aiotlib.account.IAccountInfo impl) {
      if (Stub.Proxy.sDefaultImpl == null && impl != null) {
        Stub.Proxy.sDefaultImpl = impl;
        return true;
      }
      return false;
    }
    public static com.swaiot.aiotlib.account.IAccountInfo getDefaultImpl() {
      return Stub.Proxy.sDefaultImpl;
    }
  }
  public void onInitAccountInfo(java.lang.String accountInfo) throws android.os.RemoteException;
  public void onAccountChange(boolean isLogin, java.lang.String accountInfo) throws android.os.RemoteException;
}
