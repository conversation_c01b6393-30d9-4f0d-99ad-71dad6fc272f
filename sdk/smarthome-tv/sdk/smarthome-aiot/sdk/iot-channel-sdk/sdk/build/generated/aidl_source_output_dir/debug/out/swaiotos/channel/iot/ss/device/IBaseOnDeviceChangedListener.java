/*
 * This file is auto-generated.  DO NOT MODIFY.
 */
package swaiotos.channel.iot.ss.device;
public interface IBaseOnDeviceChangedListener extends android.os.IInterface
{
/** Local-side IPC implementation stub class. */
public static abstract class Stub extends android.os.Binder implements swaiotos.channel.iot.ss.device.IBaseOnDeviceChangedListener
{
private static final java.lang.String DESCRIPTOR = "swaiotos.channel.iot.ss.device.IBaseOnDeviceChangedListener";
/** Construct the stub at attach it to the interface. */
public Stub()
{
this.attachInterface(this, DESCRIPTOR);
}
/**
 * Cast an IBinder object into an swaiotos.channel.iot.ss.device.IBaseOnDeviceChangedListener interface,
 * generating a proxy if needed.
 */
public static swaiotos.channel.iot.ss.device.IBaseOnDeviceChangedListener asInterface(android.os.IBinder obj)
{
if ((obj==null)) {
return null;
}
android.os.IInterface iin = obj.queryLocalInterface(DESCRIPTOR);
if (((iin!=null)&&(iin instanceof swaiotos.channel.iot.ss.device.IBaseOnDeviceChangedListener))) {
return ((swaiotos.channel.iot.ss.device.IBaseOnDeviceChangedListener)iin);
}
return new swaiotos.channel.iot.ss.device.IBaseOnDeviceChangedListener.Stub.Proxy(obj);
}
@Override public android.os.IBinder asBinder()
{
return this;
}
@Override public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws android.os.RemoteException
{
java.lang.String descriptor = DESCRIPTOR;
switch (code)
{
case INTERFACE_TRANSACTION:
{
reply.writeString(descriptor);
return true;
}
case TRANSACTION_onDeviceOffLine:
{
data.enforceInterface(descriptor);
swaiotos.channel.iot.ss.device.Device _arg0;
if ((0!=data.readInt())) {
_arg0 = swaiotos.channel.iot.ss.device.Device.CREATOR.createFromParcel(data);
}
else {
_arg0 = null;
}
this.onDeviceOffLine(_arg0);
reply.writeNoException();
return true;
}
case TRANSACTION_onDeviceOnLine:
{
data.enforceInterface(descriptor);
swaiotos.channel.iot.ss.device.Device _arg0;
if ((0!=data.readInt())) {
_arg0 = swaiotos.channel.iot.ss.device.Device.CREATOR.createFromParcel(data);
}
else {
_arg0 = null;
}
this.onDeviceOnLine(_arg0);
reply.writeNoException();
return true;
}
case TRANSACTION_onDeviceUpdate:
{
data.enforceInterface(descriptor);
swaiotos.channel.iot.ss.device.Device _arg0;
if ((0!=data.readInt())) {
_arg0 = swaiotos.channel.iot.ss.device.Device.CREATOR.createFromParcel(data);
}
else {
_arg0 = null;
}
this.onDeviceUpdate(_arg0);
reply.writeNoException();
return true;
}
default:
{
return super.onTransact(code, data, reply, flags);
}
}
}
private static class Proxy implements swaiotos.channel.iot.ss.device.IBaseOnDeviceChangedListener
{
private android.os.IBinder mRemote;
Proxy(android.os.IBinder remote)
{
mRemote = remote;
}
@Override public android.os.IBinder asBinder()
{
return mRemote;
}
public java.lang.String getInterfaceDescriptor()
{
return DESCRIPTOR;
}
@Override public void onDeviceOffLine(swaiotos.channel.iot.ss.device.Device device) throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
try {
_data.writeInterfaceToken(DESCRIPTOR);
if ((device!=null)) {
_data.writeInt(1);
device.writeToParcel(_data, 0);
}
else {
_data.writeInt(0);
}
mRemote.transact(Stub.TRANSACTION_onDeviceOffLine, _data, _reply, 0);
_reply.readException();
}
finally {
_reply.recycle();
_data.recycle();
}
}
@Override public void onDeviceOnLine(swaiotos.channel.iot.ss.device.Device device) throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
try {
_data.writeInterfaceToken(DESCRIPTOR);
if ((device!=null)) {
_data.writeInt(1);
device.writeToParcel(_data, 0);
}
else {
_data.writeInt(0);
}
mRemote.transact(Stub.TRANSACTION_onDeviceOnLine, _data, _reply, 0);
_reply.readException();
}
finally {
_reply.recycle();
_data.recycle();
}
}
@Override public void onDeviceUpdate(swaiotos.channel.iot.ss.device.Device device) throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
try {
_data.writeInterfaceToken(DESCRIPTOR);
if ((device!=null)) {
_data.writeInt(1);
device.writeToParcel(_data, 0);
}
else {
_data.writeInt(0);
}
mRemote.transact(Stub.TRANSACTION_onDeviceUpdate, _data, _reply, 0);
_reply.readException();
}
finally {
_reply.recycle();
_data.recycle();
}
}
}
static final int TRANSACTION_onDeviceOffLine = (android.os.IBinder.FIRST_CALL_TRANSACTION + 0);
static final int TRANSACTION_onDeviceOnLine = (android.os.IBinder.FIRST_CALL_TRANSACTION + 1);
static final int TRANSACTION_onDeviceUpdate = (android.os.IBinder.FIRST_CALL_TRANSACTION + 2);
}
public void onDeviceOffLine(swaiotos.channel.iot.ss.device.Device device) throws android.os.RemoteException;
public void onDeviceOnLine(swaiotos.channel.iot.ss.device.Device device) throws android.os.RemoteException;
public void onDeviceUpdate(swaiotos.channel.iot.ss.device.Device device) throws android.os.RemoteException;
}
