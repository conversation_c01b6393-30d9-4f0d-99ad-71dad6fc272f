package com.swaiot.aiotlib.service.binder.device;

import android.os.RemoteException;

import com.swaiot.aiotlib.common.util.EmptyUtils;
import com.swaiot.aiotlib.common.util.LogUtil;
import com.swaiot.aiotlib.device.apconfig.IApconfigDevice;
import com.swaiot.aiotlib.device.apconfig.IMideaApconfig;
import com.swaiot.aiotlib.device.apconfig.ISkyworthApconfig;
import com.swaiot.aiotlib.device.apconfig.MideaApconfig;
import com.swaiot.aiotlib.device.apconfig.SkyworthApconfig;
import com.swaiot.aiotlib.service.binder.push.AIOTPushServiceBinder;

/**
 * @ClassName: DeviceApconfigBinder
 * @Author: AwenZeng
 * @CreateDate: 2020/6/22 21:15
 * @Description:
 */
public class DeviceApconfigBinder extends IApconfigDevice.Stub {
    private IMideaApconfig mMideaApconfig;
    private ISkyworthApconfig mSkyworthApconfig;

    public DeviceApconfigBinder(AIOTPushServiceBinder aiotPushServiceBinder) {
        mMideaApconfig = new MideaApconfig(aiotPushServiceBinder);
        mSkyworthApconfig = new SkyworthApconfig();
    }

    @Override
    public void startApconfig(String wifiSSID, String wifiPassword, String deviceSSID,String param) throws RemoteException {
        LogUtil.androidLog(  "startApconfig() called with: wifiSSID = [" + wifiSSID + "], wifiPassword = [" + wifiPassword + "], deviceSSID = [" + deviceSSID + "]");
        if(isSkyWorthDevice(deviceSSID)){
            mSkyworthApconfig.startConfigSkyworth(wifiSSID,wifiPassword,deviceSSID);
        }else{
            mMideaApconfig.startConfigMidea(EmptyUtils.handleNullString(wifiSSID),EmptyUtils.handleNullString(wifiPassword),EmptyUtils.handleNullString(deviceSSID),EmptyUtils.handleNullString(param));
        }
    }

    @Override
    public void stopApconfig(String deviceSSID) throws RemoteException {
        LogUtil.androidLog(  "stopApconfig() deviceSSID：" + deviceSSID);
        if(isSkyWorthDevice(deviceSSID)){
            mSkyworthApconfig.stopConfigSkyWorth();
        }else{
            mMideaApconfig.stopConfigMidea();
        }
    }

    @Override
    public void onNetworkChange(boolean isConnect, String ssid) throws RemoteException {
        LogUtil.androidLog(  "onNetworkChange() called with: isConnect = [" + isConnect + "], ssid = [" + ssid + "]");
        if(isSkyWorthDevice(ssid)){

        }
    }

    /**
     * 是否是创维设备
     * @param ssid
     * @return
     */
    private boolean isSkyWorthDevice(String ssid){
        if (EmptyUtils.isNotEmpty(ssid)&&ssid.startsWith("midea_")){
            return false;
        }else{
            return true;
        }
    }

}
