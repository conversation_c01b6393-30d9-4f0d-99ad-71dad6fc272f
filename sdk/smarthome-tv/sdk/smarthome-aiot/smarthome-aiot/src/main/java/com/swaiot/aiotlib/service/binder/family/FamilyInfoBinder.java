package com.swaiot.aiotlib.service.binder.family;

import android.os.RemoteException;

import com.swaiot.aiotlib.common.model.AiotConstants;
import com.swaiot.aiotlib.common.util.EmptyUtils;
import com.swaiot.aiotlib.common.util.LogUtil;
import com.swaiot.aiotlib.family.IFamilyInfo;
import com.swaiot.lib.SkyAIOTContract;

/**
 * @ClassName: FamilyInfoBinder
 * @Author: AwenZeng
 * @CreateDate: 2020/5/8 18:48
 * @Description:
 */
public class FamilyInfoBinder extends IFamilyInfo.Stub {
    @Override
    public String getFamilyList(String commandId) throws RemoteException {
        LogUtil.androidLog("getFamilyList:"+ commandId);
        SkyAIOTContract.require_resource(AiotConstants.KEY_HTTP_FAMILY_LIST,  EmptyUtils.handleNullString(commandId),(byte)1);
        return null;
    }

    @Override
    public String getFamilyStatusData(String familyId, String commandId) throws RemoteException {
        SkyAIOTContract.require_resource(AiotConstants.KEY_HTTP_HOME_STATUS, EmptyUtils.handleNullString(commandId),(byte)1);
        return null;
    }
}
