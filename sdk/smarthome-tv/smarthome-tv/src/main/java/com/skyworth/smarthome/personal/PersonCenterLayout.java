package com.skyworth.smarthome.personal;

import android.app.Activity;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.net.Uri;
import android.os.Bundle;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.Switch;
import android.widget.TextView;

import com.skyworth.smarthome.common.bean.DeviceInfo;
import com.skyworth.smarthome.common.bean.PersonCenterBean;
import com.skyworth.smarthome.common.dialog.CommonDialog;
import com.skyworth.smarthome.common.model.AppData;
import com.skyworth.smarthome.common.model.UserInfo;
import com.skyworth.smarthome.common.ui.MarqueeText;
import com.skyworth.smarthome.common.util.CommonUtil;
import com.skyworth.smarthome.common.util.Contants;
import com.skyworth.smarthome.common.util.DataCacheUtil;
import com.skyworth.smarthome.common.util.SystemProperty;
import com.skyworth.smarthome.common.util.ThreadManager;
import com.skyworth.smarthome.common.util.Utils;
import com.skyworth.smarthome.common.util.ViewsBuilder;
import com.skyworth.smarthome.personal.thirdaccount.ThirdAccountActivity;
import com.skyworth.smarthome.personal.unbinddevice.UnbindDeviceActivity;
import com.skyworth.smarthome.service.model.ISmartDeviceDataModel;
import com.skyworth.smarthome.R;
import com.skyworth.smarthome.account.AppAccountManager;
import com.skyworth.ui.api.SkyDialogView;
import com.skyworth.util.Util;
import com.skyworth.util.imageloader.ImageLoader;
import com.smarthome.common.utils.EmptyUtils;
import com.swaiot.aiotlib.common.entity.FamilyBean;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/8/17
 */
public class PersonCenterLayout implements PersonCenterContract.View, View.OnClickListener, View.OnKeyListener {

    private final String TAG = PersonCenterLayout.class.getSimpleName();
    private Context mContext;
    private View mMainView;
    private PersonCenterContract.Presenter mPresenter;
    private List<PersonCenterBean> mFamliyBeans = new ArrayList<>();
    private String mCurrentFamilyId;
    private TextView mLogoTextView, mDevicesOrPhoneTextView/*,mSwitchTextView*/, mPhoneNumberTextView, mAutoFindDeviceTextView;
    private MarqueeText mSwitchTextView;
    private ImageView mRightLogoImageView;
    private View mLogoView;
    private PostDelayRunnable mPostDelayRunnable;
    private boolean mAutoFindDeviceStatus = false;

    public PersonCenterLayout(Context context) {
        mContext = context;
        mMainView = ViewsBuilder.getPersonCenter2(getContext());
        RelativeLayout enableServiceLayout = mMainView.findViewById(R.id.person_center_enable_service);
        RelativeLayout switchLayout = mMainView.findViewById(R.id.person_center_family);
        RelativeLayout relativeLayout = mMainView.findViewById(R.id.person_center_relate_account);
        RelativeLayout unBindLayout = mMainView.findViewById(R.id.person_center_unbind_device);
        RelativeLayout phoneLayout = mMainView.findViewById(R.id.person_center_phone);
        RelativeLayout findLayout = mMainView.findViewById(R.id.person_center_find_device);
        RelativeLayout netLayout = mMainView.findViewById(R.id.person_center_net_switch);
        findLayout.setVisibility(View.GONE);

        TextView logoutTextView = mMainView.findViewById(R.id.person_center_logout);

        mLogoView = mMainView.findViewById(R.id.person_center_family_logo);
        mLogoTextView = mMainView.findViewById(R.id.person_center_family_name);
        mDevicesOrPhoneTextView = mMainView.findViewById(R.id.person_center_family_devices);
        mAutoFindDeviceTextView = mMainView.findViewById(R.id.person_center_find_device_tips);

        mSwitchTextView = mMainView.findViewById(R.id.person_center_switch_family_name);

        mPhoneNumberTextView = mMainView.findViewById(R.id.person_center_phone_number);
        mRightLogoImageView = mMainView.findViewById(R.id.person_center_right);

        enableServiceLayout.setOnClickListener(this);
        switchLayout.setOnClickListener(this);
        relativeLayout.setOnClickListener(this);
        unBindLayout.setOnClickListener(this);
        logoutTextView.setOnClickListener(this);
        findLayout.setOnClickListener(this);
        phoneLayout.setOnClickListener(this);
        netLayout.setOnClickListener(this);

        enableServiceLayout.setOnKeyListener(this);
        relativeLayout.setOnKeyListener(this);
        unBindLayout.setOnKeyListener(this);
        logoutTextView.setOnKeyListener(this);
        switchLayout.setOnKeyListener(this);
        findLayout.setOnKeyListener(this);
        phoneLayout.setOnKeyListener(this);
        netLayout.setOnKeyListener(this);

        if(SystemProperty.isStoreMode()){
            findLayout.setVisibility(View.VISIBLE);
        }
        setData();
    }

    private Context getContext() {
        return mContext;
    }

    public View getView() {
        return mMainView;
    }

    private void setData() {
        PersonCenterPresenter mPresenter = new PersonCenterPresenter(this);
        mPresenter.init(getContext());

        if (!TextUtils.isEmpty(UserInfo.getInstance().getAvatar())) {
            ImageLoader.getLoader()
                    .with(getContext())
                    .load(Uri.parse(UserInfo.getInstance().getAvatar()))
                    .resize(Util.Div(120), Util.Div(120))
                    .setScaleType(ImageView.ScaleType.FIT_XY)
                    .setPlaceHolder(getContext().getResources().getDrawable(R.drawable.scaning_circle))
                    .setLeftBottomCorner(Util.Div(60))
                    .setLeftTopCorner(Util.Div(60))
                    .setRightBottomCorner(Util.Div(60))
                    .setRightTopCorner(Util.Div(60))
                    .into(mLogoView);
        }
        List<FamilyBean> familyList = AppData.getInstance().getFamilyList();
        if (AppData.getInstance().getCurrentFamily() != null) {
            if (familyList == null) {
                familyList = new ArrayList<>();
                familyList.add(AppData.getInstance().getCurrentFamily());
            } else if (familyList.size() == 0) {
                familyList.add(AppData.getInstance().getCurrentFamily());
            }
        }

        if (familyList != null && familyList.size() > 0) {
            for (int i = 0; i < familyList.size(); i++) {
                FamilyBean familyBean = familyList.get(i);
                PersonCenterBean personCenterBean = new PersonCenterBean();
                personCenterBean.setFamilyBean(familyBean);
                if (AppData.getInstance().getCurrentFamilyId().equals(familyBean.family_id)) {
                    List<DeviceInfo> deviceInfoList = AppData.getInstance().getDeviceInfoList();
                    if (EmptyUtils.isNotEmpty(deviceInfoList) && !TextUtils.isEmpty(deviceInfoList.get(0).familyId) &&
                            deviceInfoList.get(0).familyId.equals(familyBean.family_id)) {
                        personCenterBean.setDeviceNumber(deviceInfoList.size());
                    }
                } else {
                    personCenterBean.setDeviceNumber(-1);
                }
                mFamliyBeans.add(personCenterBean);
            }
        }
        mCurrentFamilyId = AppData.getInstance().getCurrentFamilyId();

        String phoneNumer = UserInfo.getInstance().getMobilePhone();
        if (!TextUtils.isEmpty(phoneNumer) && phoneNumer.length() >= 6) {
            mRightLogoImageView.setVisibility(View.INVISIBLE);

            RelativeLayout.LayoutParams tipsRL = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT,
                    RelativeLayout.LayoutParams.WRAP_CONTENT);
            tipsRL.rightMargin = Util.Div(40);
            tipsRL.addRule(RelativeLayout.ALIGN_PARENT_RIGHT);
            tipsRL.addRule(RelativeLayout.CENTER_VERTICAL);
            mPhoneNumberTextView.setLayoutParams(tipsRL);

            StringBuffer number = new StringBuffer();
            for (int i = 0; i < phoneNumer.length() - 6; i++) {
                number.append("*");
            }
            mPhoneNumberTextView.setText(phoneNumer.substring(0, 3) + number + phoneNumer.substring(phoneNumer.length() - 3, phoneNumer.length()));
            mDevicesOrPhoneTextView.setText(String.format(getContext().getResources().getString(R.string.person_center_devices), "" + 0));
        } else {
            mPhoneNumberTextView.setText(getContext().getResources().getString(R.string.person_center_bind_device));
            if (!TextUtils.isEmpty(UserInfo.getInstance().getNick_name()))
                mLogoTextView.setText(UserInfo.getInstance().getNick_name());
            mDevicesOrPhoneTextView.setText(getContext().getResources().getString(R.string.person_center_wait_bind));
        }

        if ((mFamliyBeans == null || mFamliyBeans.size() <= 0) || TextUtils.isEmpty(phoneNumer)) {
            mSwitchTextView.setText(getContext().getResources().getString(R.string.person_center_family_nothing));
            if (!TextUtils.isEmpty(UserInfo.getInstance().getNick_name()))
                mLogoTextView.setText(UserInfo.getInstance().getNick_name());
        } else {
            String familyName = AppData.getInstance().getCurrentFamily().family_name;
            mLogoTextView.setText(familyName);
            mSwitchTextView.setText(familyName);
        }

        mAutoFindDeviceStatus = DataCacheUtil.getInstance().getBoolean(DataCacheUtil.KEY_GET_AUTO_FIND_DEVICE_STATUS, true);
        setAutofindDevice();
    }

    public void onResume() {
        if (TextUtils.isEmpty(UserInfo.getInstance().getMobilePhone())) {
            return;
        }
        queryBindDevices(ISmartDeviceDataModel.INSTANCE.getCacheSmartDeviceList());
    }

    private void setAutofindDevice() {

        String stausFind = mAutoFindDeviceStatus ? getContext().getResources().getString(R.string.person_center_find_device_open)
                : getContext().getResources().getString(R.string.person_center_find_device_close);
        mAutoFindDeviceTextView.setText(stausFind);
        if (mPresenter != null) {
            mPresenter.autoFindDevice(mAutoFindDeviceStatus);
        }
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.person_center_logout) {
            final CommonDialog dialogView = new CommonDialog(getContext(), true);
            dialogView.setTipsString("是否要退出当前登录？", null);
            dialogView.setOnDialogOnKeyListener(new SkyDialogView.OnDialogOnKeyListener() {
                @Override
                public boolean onDialogOnKeyEvent(int keyCode, KeyEvent event) {
                    return false;
                }

                @Override
                public void onFirstBtnOnClickEvent() {
                    dialogView.dismiss();
                    //退出登录
                    mPresenter.logout();
                }

                @Override
                public void onSecondBtnOnClickEvent() {
                    dialogView.dismiss();
                }
            });
            dialogView.show();

        } else if (v.getId() == R.id.person_center_enable_service) {
            //启用服务
            toggleEnableService();
        } else if (v.getId() == R.id.person_center_family) {
            //切换家庭
            switchFamilyInfo();
        } else if (v.getId() == R.id.person_center_relate_account) {
            //关联账号
            gotoRelatePage();
        } else if (v.getId() == R.id.person_center_unbind_device) {
            //解绑设备
            gotoUnbindPage();
        } else if (v.getId() == R.id.person_center_phone) {
            //手机号
            String phoneNumer = UserInfo.getInstance().getMobilePhone();
            if (TextUtils.isEmpty(phoneNumer) || phoneNumer.length() <= 0) {
                gotoPhonePage();
            }
        } else if (v.getId() == R.id.person_center_find_device) {
            mAutoFindDeviceStatus = !mAutoFindDeviceStatus;
            setAutofindDevice();
        } else if (v.getId() == R.id.person_center_net_switch) {
            netToggle(v);
        }
    }

    private void netToggle(View v) {
        if (v instanceof RelativeLayout) {
            RelativeLayout rl_netSwitch = (RelativeLayout) v;
            RelativeLayout child = (RelativeLayout) rl_netSwitch.getChildAt(0);
            View lastChild = child.getChildAt(child.getChildCount() - 1);
            if (lastChild instanceof Switch) {
                Switch lastChildSwitch = (Switch) lastChild;
                lastChildSwitch.setChecked(!lastChildSwitch.isChecked());
                try {
                    int netSwitch = Settings.System.getInt(getContext().getContentResolver(), "netSwitch");
                    Log.d(TAG, "netToggle: " + netSwitch);
                    Settings.System.putInt(getContext().getContentResolver(),"netSwitch",netSwitch == 2 ? 1 : 2);
                    PackageManager packageManager = mContext.getPackageManager();
                    if (Utils.checkApkExist(mContext,"swaiotos.channel.iot")) {
                        if (netSwitch == 2) {
                            packageManager.setApplicationEnabledSetting("swaiotos.channel.iot", PackageManager.COMPONENT_ENABLED_STATE_DISABLED, 0);
                        } else if (netSwitch == 1) {
                            packageManager.setApplicationEnabledSetting("swaiotos.channel.iot", PackageManager.COMPONENT_ENABLED_STATE_ENABLED, 0);
                        }
                    }
                } catch (Settings.SettingNotFoundException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private void toggleEnableService() {
        // 获取启用服务的布局
        RelativeLayout enableServiceLayout = mMainView.findViewById(R.id.person_center_enable_service);
        if (enableServiceLayout instanceof RelativeLayout) {
            RelativeLayout child = (RelativeLayout) enableServiceLayout.getChildAt(0);
            View lastChild = child.getChildAt(child.getChildCount() - 1);
            if (lastChild instanceof Switch) {
                Switch serviceSwitch = (Switch) lastChild;
                // 切换开关状态
                boolean newStatus = !serviceSwitch.isChecked();
                serviceSwitch.setChecked(newStatus);
                // 保存到缓存
                DataCacheUtil.getInstance().putBoolean(DataCacheUtil.KEY_ENABLE_SERVICE_STATUS, newStatus);
                Log.d(TAG, "toggleEnableService: " + newStatus);
            }
        }
    }

    @Override
    public boolean onKey(View v, int keyCode, KeyEvent event) {
        if (event.getAction() == KeyEvent.ACTION_DOWN) {
            if (event.getKeyCode() == KeyEvent.KEYCODE_DPAD_RIGHT) {
                if(v.getId() == R.id.person_center_enable_service){
                    //启用服务,右键
                    toggleEnableService();
                }else if(v.getId() == R.id.person_center_family){
                    //切换家庭,右键
                    switchFamilyInfo();
                }else if(v.getId() == R.id.person_center_relate_account){
                    //goto 关联账号
                    gotoRelatePage();
                }else if(v.getId() == R.id.person_center_unbind_device){
                    //解绑设备
                    gotoUnbindPage();
                }
                else if(v.getId() == R.id.person_center_phone){
                    //手机号
                    String phoneNumer = UserInfo.getInstance().getMobilePhone();
                    if (TextUtils.isEmpty(phoneNumer) || phoneNumer.length() <= 0) {
                        gotoPhonePage();
                    }
                }
                else if(v.getId() == R.id.person_center_find_device){
                    //自动发现设备开关
                    mAutoFindDeviceStatus = !mAutoFindDeviceStatus;
                    setAutofindDevice();
                } else if (v.getId() == R.id.person_center_net_switch) {
                    netToggle(v);
                }
            } else if (event.getKeyCode() == KeyEvent.KEYCODE_DPAD_LEFT) {
                if (v.getId() == R.id.person_center_family) {
                    //切换家庭,左键
                    switchFamilyInfoLeft();
                } else if (v.getId() == R.id.person_center_find_device) {
                    //自动发现设备开关
                    mAutoFindDeviceStatus = !mAutoFindDeviceStatus;
                    setAutofindDevice();
                }
            }
        }
        return false;
    }

    private void gotoUnbindPage() {
        if (!CommonUtil.isNetConnected(getContext())) {
            return;
        }
        String phoneNumer = UserInfo.getInstance().getMobilePhone();
        if (!TextUtils.isEmpty(phoneNumer)) {
            //goto 解绑设备
            Bundle bundle = new Bundle();
            bundle.putString(Contants.COOCAA_INTENT_CURRENT_FAMILY_ID, mCurrentFamilyId);
            Intent intent = new Intent(getContext(), UnbindDeviceActivity.class);
            intent.putExtras(bundle);
            getContext().startActivity(intent);
        } else {
            ThreadManager.getInstance().ioThread(new Runnable() {
                @Override
                public void run() {
                    AppAccountManager.INSTANCE.gotoBindMobile();
                }
            });
        }
    }


    private void gotoRelatePage() {
        if (!CommonUtil.isNetConnected(getContext())) {
            return;
        }
        //goto 关联账号
        getContext().startActivity(new Intent(getContext(), ThirdAccountActivity.class));
    }

    private void gotoPhonePage() {
        if (!CommonUtil.isNetConnected(getContext())) {
            return;
        }
        //goto 手机绑定页面
        ThreadManager.getInstance().ioThread(new Runnable() {
            @Override
            public void run() {
                AppAccountManager.INSTANCE.gotoBindMobile();
            }
        });
    }

    private void switchFamily(PersonCenterBean personCenterBean) {
        mCurrentFamilyId = personCenterBean.getFamilyBean().family_id;
        String familyName = personCenterBean.getFamilyBean().family_name;
        int devicesNumber = personCenterBean.getDeviceNumber();
        Log.d(TAG, "-------------mCurrentFamilyId:" + mCurrentFamilyId + " familyName:" + familyName + " devicesNumber:" + devicesNumber);
        mLogoTextView.setText(familyName);
        mSwitchTextView.setText(familyName);
        if (mPostDelayRunnable == null)
            mPostDelayRunnable = new PostDelayRunnable();
        mPostDelayRunnable.set(personCenterBean);
        ThreadManager.getInstance().removeUiThread(mPostDelayRunnable);
        ThreadManager.getInstance().uiThread(mPostDelayRunnable, 300);
    }

    private void switchFamilyInfo() {
        if (EmptyUtils.isEmpty(UserInfo.getInstance().getMobilePhone())) {
            AppAccountManager.INSTANCE.gotoBindMobile();
            return;
        }
        if (mFamliyBeans != null && mFamliyBeans.size() > 0) {
            for (int i = 0; i < mFamliyBeans.size(); i++) {
                if (!TextUtils.isEmpty(mCurrentFamilyId) && mFamliyBeans.get(i).getFamilyBean() != null
                        && !TextUtils.isEmpty(mFamliyBeans.get(i).getFamilyBean().family_id)
                        && mCurrentFamilyId.equals(mFamliyBeans.get(i).getFamilyBean().family_id)) {
                    if (i == mFamliyBeans.size() - 1) {
                        switchFamily(mFamliyBeans.get(0));
                    } else {
                        switchFamily(mFamliyBeans.get(i + 1));
                    }
                    break;
                }
            }
        }
    }


    private void switchFamilyInfoLeft() {
        if (EmptyUtils.isEmpty(UserInfo.getInstance().getMobilePhone())) {
            AppAccountManager.INSTANCE.gotoBindMobile();
            return;
        }
        if (mFamliyBeans != null && mFamliyBeans.size() > 0) {
            for (int i = 0; i < mFamliyBeans.size(); i++) {
                if (!TextUtils.isEmpty(mCurrentFamilyId) && mFamliyBeans.get(i).getFamilyBean() != null
                        && !TextUtils.isEmpty(mFamliyBeans.get(i).getFamilyBean().family_id)
                        && mCurrentFamilyId.equals(mFamliyBeans.get(i).getFamilyBean().family_id)) {
                    if (i == 0) {
                        switchFamily(mFamliyBeans.get(mFamliyBeans.size() - 1));
                    } else {
                        switchFamily(mFamliyBeans.get(i - 1));
                    }
                    break;
                }
            }
        }
    }

    @Override
    public void finish() {
        if (getContext() instanceof Activity) {
            ((Activity) getContext()).finish();
        }
    }

    @Override
    public void queryBindDevices(List<DeviceInfo> deviceBeans) {
        int size = 0;
        if (EmptyUtils.isNotEmpty(deviceBeans)) {
            size = deviceBeans.get(0).is_virtual ? 0 : deviceBeans.size();
        }
        for (int i = 0; i < mFamliyBeans.size(); i++) {
            if (!TextUtils.isEmpty(AppData.getInstance().getCurrentFamilyId()) &&
                    AppData.getInstance().getCurrentFamilyId().equals(mFamliyBeans.get(i).getFamilyBean().family_id)) {
                mFamliyBeans.get(i).setDeviceNumber(size);
                if (isActive()) {
                    final int finalSize = size;
                    ThreadManager.getInstance().uiThread(new Runnable() {
                        @Override
                        public void run() {
                            mDevicesOrPhoneTextView.setText(String.format(getContext().getResources().getString(R.string.person_center_devices), "" + finalSize));
                        }
                    });
                }
            }
        }
    }

    @Override
    public void setPresenter(PersonCenterContract.Presenter presenter) {
        mPresenter = presenter;
    }

    @Override
    public boolean isActive() {
        if (getContext() != null && getContext() instanceof Activity) {
            return !((Activity) getContext()).isFinishing() && !((Activity) getContext()).isDestroyed();
        }
        return true;
    }

    public void onDestroy() {
        if (mPresenter != null) {
            mPresenter.detachView();
        }
    }

    class PostDelayRunnable implements Runnable {
        private PersonCenterBean personCenterBean;

        public void set(PersonCenterBean personCenterBean) {
            this.personCenterBean = personCenterBean;
        }

        @Override
        public void run() {
            Log.d(TAG, "-------------xxxx--------------------");
            mPresenter.queryDevices(personCenterBean.getFamilyBean().family_id);
        }
    }
}
