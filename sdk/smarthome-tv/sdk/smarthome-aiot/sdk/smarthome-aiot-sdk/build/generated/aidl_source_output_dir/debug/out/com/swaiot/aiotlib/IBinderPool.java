/*
 * This file is auto-generated.  DO NOT MODIFY.
 */
package com.swaiot.aiotlib;
/**
 * Describe:
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/2/25
 */
public interface IBinderPool extends android.os.IInterface
{
  /** Default implementation for IBinderPool. */
  public static class Default implements com.swaiot.aiotlib.IBinderPool
  {
    @Override public android.os.IBinder queryBinder(int binderCode) throws android.os.RemoteException
    {
      return null;
    }
    @Override public void requireResource(java.lang.String resourceType, java.lang.String commandId) throws android.os.RemoteException
    {
    }
    @Override public void controlObject(java.lang.String objectType, java.lang.String deviceId, java.lang.String data, java.lang.String commandId) throws android.os.RemoteException
    {
    }
    @Override public void watch_resources(java.lang.String resource_list) throws android.os.RemoteException
    {
    }
    @Override public void cancel_watch_resources(java.lang.String resource_list) throws android.os.RemoteException
    {
    }
    @Override public void operate_stask(java.lang.String task_type, java.lang.String operate_type, java.lang.String task_id, java.lang.String task_options) throws android.os.RemoteException
    {
    }
    @Override
    public android.os.IBinder asBinder() {
      return null;
    }
  }
  /** Local-side IPC implementation stub class. */
  public static abstract class Stub extends android.os.Binder implements com.swaiot.aiotlib.IBinderPool
  {
    private static final java.lang.String DESCRIPTOR = "com.swaiot.aiotlib.IBinderPool";
    /** Construct the stub at attach it to the interface. */
    public Stub()
    {
      this.attachInterface(this, DESCRIPTOR);
    }
    /**
     * Cast an IBinder object into an com.swaiot.aiotlib.IBinderPool interface,
     * generating a proxy if needed.
     */
    public static com.swaiot.aiotlib.IBinderPool asInterface(android.os.IBinder obj)
    {
      if ((obj==null)) {
        return null;
      }
      android.os.IInterface iin = obj.queryLocalInterface(DESCRIPTOR);
      if (((iin!=null)&&(iin instanceof com.swaiot.aiotlib.IBinderPool))) {
        return ((com.swaiot.aiotlib.IBinderPool)iin);
      }
      return new com.swaiot.aiotlib.IBinderPool.Stub.Proxy(obj);
    }
    @Override public android.os.IBinder asBinder()
    {
      return this;
    }
    @Override public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws android.os.RemoteException
    {
      java.lang.String descriptor = DESCRIPTOR;
      switch (code)
      {
        case INTERFACE_TRANSACTION:
        {
          reply.writeString(descriptor);
          return true;
        }
        case TRANSACTION_queryBinder:
        {
          data.enforceInterface(descriptor);
          int _arg0;
          _arg0 = data.readInt();
          android.os.IBinder _result = this.queryBinder(_arg0);
          reply.writeNoException();
          reply.writeStrongBinder(_result);
          return true;
        }
        case TRANSACTION_requireResource:
        {
          data.enforceInterface(descriptor);
          java.lang.String _arg0;
          _arg0 = data.readString();
          java.lang.String _arg1;
          _arg1 = data.readString();
          this.requireResource(_arg0, _arg1);
          reply.writeNoException();
          return true;
        }
        case TRANSACTION_controlObject:
        {
          data.enforceInterface(descriptor);
          java.lang.String _arg0;
          _arg0 = data.readString();
          java.lang.String _arg1;
          _arg1 = data.readString();
          java.lang.String _arg2;
          _arg2 = data.readString();
          java.lang.String _arg3;
          _arg3 = data.readString();
          this.controlObject(_arg0, _arg1, _arg2, _arg3);
          reply.writeNoException();
          return true;
        }
        case TRANSACTION_watch_resources:
        {
          data.enforceInterface(descriptor);
          java.lang.String _arg0;
          _arg0 = data.readString();
          this.watch_resources(_arg0);
          reply.writeNoException();
          return true;
        }
        case TRANSACTION_cancel_watch_resources:
        {
          data.enforceInterface(descriptor);
          java.lang.String _arg0;
          _arg0 = data.readString();
          this.cancel_watch_resources(_arg0);
          reply.writeNoException();
          return true;
        }
        case TRANSACTION_operate_stask:
        {
          data.enforceInterface(descriptor);
          java.lang.String _arg0;
          _arg0 = data.readString();
          java.lang.String _arg1;
          _arg1 = data.readString();
          java.lang.String _arg2;
          _arg2 = data.readString();
          java.lang.String _arg3;
          _arg3 = data.readString();
          this.operate_stask(_arg0, _arg1, _arg2, _arg3);
          reply.writeNoException();
          return true;
        }
        default:
        {
          return super.onTransact(code, data, reply, flags);
        }
      }
    }
    private static class Proxy implements com.swaiot.aiotlib.IBinderPool
    {
      private android.os.IBinder mRemote;
      Proxy(android.os.IBinder remote)
      {
        mRemote = remote;
      }
      @Override public android.os.IBinder asBinder()
      {
        return mRemote;
      }
      public java.lang.String getInterfaceDescriptor()
      {
        return DESCRIPTOR;
      }
      @Override public android.os.IBinder queryBinder(int binderCode) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        android.os.IBinder _result;
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeInt(binderCode);
          boolean _status = mRemote.transact(Stub.TRANSACTION_queryBinder, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            return getDefaultImpl().queryBinder(binderCode);
          }
          _reply.readException();
          _result = _reply.readStrongBinder();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
        return _result;
      }
      @Override public void requireResource(java.lang.String resourceType, java.lang.String commandId) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(resourceType);
          _data.writeString(commandId);
          boolean _status = mRemote.transact(Stub.TRANSACTION_requireResource, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().requireResource(resourceType, commandId);
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public void controlObject(java.lang.String objectType, java.lang.String deviceId, java.lang.String data, java.lang.String commandId) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(objectType);
          _data.writeString(deviceId);
          _data.writeString(data);
          _data.writeString(commandId);
          boolean _status = mRemote.transact(Stub.TRANSACTION_controlObject, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().controlObject(objectType, deviceId, data, commandId);
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public void watch_resources(java.lang.String resource_list) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(resource_list);
          boolean _status = mRemote.transact(Stub.TRANSACTION_watch_resources, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().watch_resources(resource_list);
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public void cancel_watch_resources(java.lang.String resource_list) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(resource_list);
          boolean _status = mRemote.transact(Stub.TRANSACTION_cancel_watch_resources, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().cancel_watch_resources(resource_list);
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public void operate_stask(java.lang.String task_type, java.lang.String operate_type, java.lang.String task_id, java.lang.String task_options) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(task_type);
          _data.writeString(operate_type);
          _data.writeString(task_id);
          _data.writeString(task_options);
          boolean _status = mRemote.transact(Stub.TRANSACTION_operate_stask, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().operate_stask(task_type, operate_type, task_id, task_options);
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      public static com.swaiot.aiotlib.IBinderPool sDefaultImpl;
    }
    static final int TRANSACTION_queryBinder = (android.os.IBinder.FIRST_CALL_TRANSACTION + 0);
    static final int TRANSACTION_requireResource = (android.os.IBinder.FIRST_CALL_TRANSACTION + 1);
    static final int TRANSACTION_controlObject = (android.os.IBinder.FIRST_CALL_TRANSACTION + 2);
    static final int TRANSACTION_watch_resources = (android.os.IBinder.FIRST_CALL_TRANSACTION + 3);
    static final int TRANSACTION_cancel_watch_resources = (android.os.IBinder.FIRST_CALL_TRANSACTION + 4);
    static final int TRANSACTION_operate_stask = (android.os.IBinder.FIRST_CALL_TRANSACTION + 5);
    public static boolean setDefaultImpl(com.swaiot.aiotlib.IBinderPool impl) {
      if (Stub.Proxy.sDefaultImpl == null && impl != null) {
        Stub.Proxy.sDefaultImpl = impl;
        return true;
      }
      return false;
    }
    public static com.swaiot.aiotlib.IBinderPool getDefaultImpl() {
      return Stub.Proxy.sDefaultImpl;
    }
  }
  public android.os.IBinder queryBinder(int binderCode) throws android.os.RemoteException;
  public void requireResource(java.lang.String resourceType, java.lang.String commandId) throws android.os.RemoteException;
  public void controlObject(java.lang.String objectType, java.lang.String deviceId, java.lang.String data, java.lang.String commandId) throws android.os.RemoteException;
  public void watch_resources(java.lang.String resource_list) throws android.os.RemoteException;
  public void cancel_watch_resources(java.lang.String resource_list) throws android.os.RemoteException;
  public void operate_stask(java.lang.String task_type, java.lang.String operate_type, java.lang.String task_id, java.lang.String task_options) throws android.os.RemoteException;
}
