/*
 * This file is auto-generated.  DO NOT MODIFY.
 */
package swaiotos.channel.iot.ss.session;
public interface IOnMySessionUpdateListener extends android.os.IInterface
{
/** Local-side IPC implementation stub class. */
public static abstract class Stub extends android.os.Binder implements swaiotos.channel.iot.ss.session.IOnMySessionUpdateListener
{
private static final java.lang.String DESCRIPTOR = "swaiotos.channel.iot.ss.session.IOnMySessionUpdateListener";
/** Construct the stub at attach it to the interface. */
public Stub()
{
this.attachInterface(this, DESCRIPTOR);
}
/**
 * Cast an IBinder object into an swaiotos.channel.iot.ss.session.IOnMySessionUpdateListener interface,
 * generating a proxy if needed.
 */
public static swaiotos.channel.iot.ss.session.IOnMySessionUpdateListener asInterface(android.os.IBinder obj)
{
if ((obj==null)) {
return null;
}
android.os.IInterface iin = obj.queryLocalInterface(DESCRIPTOR);
if (((iin!=null)&&(iin instanceof swaiotos.channel.iot.ss.session.IOnMySessionUpdateListener))) {
return ((swaiotos.channel.iot.ss.session.IOnMySessionUpdateListener)iin);
}
return new swaiotos.channel.iot.ss.session.IOnMySessionUpdateListener.Stub.Proxy(obj);
}
@Override public android.os.IBinder asBinder()
{
return this;
}
@Override public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws android.os.RemoteException
{
java.lang.String descriptor = DESCRIPTOR;
switch (code)
{
case INTERFACE_TRANSACTION:
{
reply.writeString(descriptor);
return true;
}
case TRANSACTION_onMySessionUpdate:
{
data.enforceInterface(descriptor);
swaiotos.channel.iot.ss.session.Session _arg0;
if ((0!=data.readInt())) {
_arg0 = swaiotos.channel.iot.ss.session.Session.CREATOR.createFromParcel(data);
}
else {
_arg0 = null;
}
this.onMySessionUpdate(_arg0);
reply.writeNoException();
return true;
}
default:
{
return super.onTransact(code, data, reply, flags);
}
}
}
private static class Proxy implements swaiotos.channel.iot.ss.session.IOnMySessionUpdateListener
{
private android.os.IBinder mRemote;
Proxy(android.os.IBinder remote)
{
mRemote = remote;
}
@Override public android.os.IBinder asBinder()
{
return mRemote;
}
public java.lang.String getInterfaceDescriptor()
{
return DESCRIPTOR;
}
/**
     * Demonstrates some basic types that you can use as parameters
     * and return values in AIDL.
     */
@Override public void onMySessionUpdate(swaiotos.channel.iot.ss.session.Session mySession) throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
try {
_data.writeInterfaceToken(DESCRIPTOR);
if ((mySession!=null)) {
_data.writeInt(1);
mySession.writeToParcel(_data, 0);
}
else {
_data.writeInt(0);
}
mRemote.transact(Stub.TRANSACTION_onMySessionUpdate, _data, _reply, 0);
_reply.readException();
}
finally {
_reply.recycle();
_data.recycle();
}
}
}
static final int TRANSACTION_onMySessionUpdate = (android.os.IBinder.FIRST_CALL_TRANSACTION + 0);
}
/**
     * Demonstrates some basic types that you can use as parameters
     * and return values in AIDL.
     */
public void onMySessionUpdate(swaiotos.channel.iot.ss.session.Session mySession) throws android.os.RemoteException;
}
