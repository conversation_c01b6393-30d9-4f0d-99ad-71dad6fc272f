package com.swaiot.aiotlib.service.model;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;

import com.swaiot.aiotlib.AIOTLib;
import com.swaiot.aiotlib.AiotLibSDK;
import com.swaiot.aiotlib.common.bean.BaiduResultBean;
import com.swaiot.aiotlib.common.http.AIOTHttpManager;
import com.swaiot.aiotlib.common.http.base.HttpApi;
import com.swaiot.aiotlib.common.http.base.HttpSubscribe;
import com.swaiot.aiotlib.common.http.base.HttpThrowable;
import com.swaiot.aiotlib.common.model.AiotAppData;
import com.swaiot.aiotlib.common.util.EmptyUtils;
import com.swaiot.aiotlib.common.util.LogUtil;

public class DeviceListSyncHelper {
    public static void deviceListForSync() {
        //当平台为Phone手机时，不上报列表
        if (AIOTLib.getDefault().getPlatform() == AiotLibSDK.Platform.PHONE)
            return;
        HttpApi.getInstance().request(AIOTHttpManager.SERVICE.deviceListForSync(), new HttpSubscribe<BaiduResultBean>() {
            @Override
            public void onSuccess(BaiduResultBean result) {
                if (EmptyUtils.isNotEmpty(result) && EmptyUtils.isNotEmpty(result.code) && result.code.equals("0")) {
                    LogUtil.androidLog("voicecmd", "deviceListSync success");
                    String baiduResult = result.baidu_result;
                    LogUtil.androidLog("voicecmd", "deviceListSync baiduResult:" + baiduResult);
                    if (EmptyUtils.isNotEmpty(baiduResult)) {
                        reportVoiceResult(AiotAppData.getInstance().getContext(), baiduResult);
                    }
                } else {
                    LogUtil.androidLog("voicecmd", "deviceListSync failed");
                }
            }

            @Override
            public void onError(HttpThrowable error) {

            }
        });
    }

    /**
     * 通过小维AI透传指令
     * @param c
     * @param response
     */
    public static void reportVoiceResult(Context c, String response) {
        try {
            Log.d("voicecmd", "responsVoice response:" + response);
            Intent in = new Intent();
            in.setAction("com.skyworth.srtnj.lafite.dueros.callback.action");
            Bundle data = new Bundle();
            data.putInt("cmd", 7);
            data.putString("data", response);
            in.putExtras(data);
            c.sendBroadcast(in);
        } catch (Exception e) {
        }
    }
}
