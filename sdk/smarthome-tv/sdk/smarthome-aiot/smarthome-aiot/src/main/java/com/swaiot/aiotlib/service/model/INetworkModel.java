package com.swaiot.aiotlib.service.model;

/**
 * @ClassName: INetworkModel
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2020/7/9 10:36
 * @Description:
 */
public interface INetworkModel {

    void setNetworkChangeListener(NetworkModel.NetworkChangeListener mListener);

    void registerNetReceiver();

    void unRegisterNetReceiver();

    boolean isNetworkConnected();

    String getCurrentWifiSSID();
}
