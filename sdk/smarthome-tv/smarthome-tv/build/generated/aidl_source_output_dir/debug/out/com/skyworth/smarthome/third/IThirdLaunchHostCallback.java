/*
 * This file is auto-generated.  DO NOT MODIFY.
 */
package com.skyworth.smarthome.third;
// Declare any non-default types here with import statements

public interface IThirdLaunchHostCallback extends android.os.IInterface
{
  /** Default implementation for IThirdLaunchHostCallback. */
  public static class Default implements com.skyworth.smarthome.third.IThirdLaunchHostCallback
  {
    @Override public java.lang.String getInfo(java.lang.String params) throws android.os.RemoteException
    {
      return null;
    }
    @Override public void exit(java.lang.String params) throws android.os.RemoteException
    {
    }
    @Override public boolean controlDevice(java.lang.String deviceId, java.util.Map params) throws android.os.RemoteException
    {
      return false;
    }
    @Override
    public android.os.IBinder asBinder() {
      return null;
    }
  }
  /** Local-side IPC implementation stub class. */
  public static abstract class Stub extends android.os.Binder implements com.skyworth.smarthome.third.IThirdLaunchHostCallback
  {
    private static final java.lang.String DESCRIPTOR = "com.skyworth.smarthome.third.IThirdLaunchHostCallback";
    /** Construct the stub at attach it to the interface. */
    public Stub()
    {
      this.attachInterface(this, DESCRIPTOR);
    }
    /**
     * Cast an IBinder object into an com.skyworth.smarthome.third.IThirdLaunchHostCallback interface,
     * generating a proxy if needed.
     */
    public static com.skyworth.smarthome.third.IThirdLaunchHostCallback asInterface(android.os.IBinder obj)
    {
      if ((obj==null)) {
        return null;
      }
      android.os.IInterface iin = obj.queryLocalInterface(DESCRIPTOR);
      if (((iin!=null)&&(iin instanceof com.skyworth.smarthome.third.IThirdLaunchHostCallback))) {
        return ((com.skyworth.smarthome.third.IThirdLaunchHostCallback)iin);
      }
      return new com.skyworth.smarthome.third.IThirdLaunchHostCallback.Stub.Proxy(obj);
    }
    @Override public android.os.IBinder asBinder()
    {
      return this;
    }
    @Override public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws android.os.RemoteException
    {
      java.lang.String descriptor = DESCRIPTOR;
      switch (code)
      {
        case INTERFACE_TRANSACTION:
        {
          reply.writeString(descriptor);
          return true;
        }
        case TRANSACTION_getInfo:
        {
          data.enforceInterface(descriptor);
          java.lang.String _arg0;
          _arg0 = data.readString();
          java.lang.String _result = this.getInfo(_arg0);
          reply.writeNoException();
          reply.writeString(_result);
          return true;
        }
        case TRANSACTION_exit:
        {
          data.enforceInterface(descriptor);
          java.lang.String _arg0;
          _arg0 = data.readString();
          this.exit(_arg0);
          reply.writeNoException();
          return true;
        }
        case TRANSACTION_controlDevice:
        {
          data.enforceInterface(descriptor);
          java.lang.String _arg0;
          _arg0 = data.readString();
          java.util.Map _arg1;
          java.lang.ClassLoader cl = (java.lang.ClassLoader)this.getClass().getClassLoader();
          _arg1 = data.readHashMap(cl);
          boolean _result = this.controlDevice(_arg0, _arg1);
          reply.writeNoException();
          reply.writeInt(((_result)?(1):(0)));
          return true;
        }
        default:
        {
          return super.onTransact(code, data, reply, flags);
        }
      }
    }
    private static class Proxy implements com.skyworth.smarthome.third.IThirdLaunchHostCallback
    {
      private android.os.IBinder mRemote;
      Proxy(android.os.IBinder remote)
      {
        mRemote = remote;
      }
      @Override public android.os.IBinder asBinder()
      {
        return mRemote;
      }
      public java.lang.String getInterfaceDescriptor()
      {
        return DESCRIPTOR;
      }
      @Override public java.lang.String getInfo(java.lang.String params) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        java.lang.String _result;
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(params);
          boolean _status = mRemote.transact(Stub.TRANSACTION_getInfo, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            return getDefaultImpl().getInfo(params);
          }
          _reply.readException();
          _result = _reply.readString();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
        return _result;
      }
      @Override public void exit(java.lang.String params) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(params);
          boolean _status = mRemote.transact(Stub.TRANSACTION_exit, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            getDefaultImpl().exit(params);
            return;
          }
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public boolean controlDevice(java.lang.String deviceId, java.util.Map params) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        boolean _result;
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(deviceId);
          _data.writeMap(params);
          boolean _status = mRemote.transact(Stub.TRANSACTION_controlDevice, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            return getDefaultImpl().controlDevice(deviceId, params);
          }
          _reply.readException();
          _result = (0!=_reply.readInt());
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
        return _result;
      }
      public static com.skyworth.smarthome.third.IThirdLaunchHostCallback sDefaultImpl;
    }
    static final int TRANSACTION_getInfo = (android.os.IBinder.FIRST_CALL_TRANSACTION + 0);
    static final int TRANSACTION_exit = (android.os.IBinder.FIRST_CALL_TRANSACTION + 1);
    static final int TRANSACTION_controlDevice = (android.os.IBinder.FIRST_CALL_TRANSACTION + 2);
    public static boolean setDefaultImpl(com.skyworth.smarthome.third.IThirdLaunchHostCallback impl) {
      if (Stub.Proxy.sDefaultImpl == null && impl != null) {
        Stub.Proxy.sDefaultImpl = impl;
        return true;
      }
      return false;
    }
    public static com.skyworth.smarthome.third.IThirdLaunchHostCallback getDefaultImpl() {
      return Stub.Proxy.sDefaultImpl;
    }
  }
  public java.lang.String getInfo(java.lang.String params) throws android.os.RemoteException;
  public void exit(java.lang.String params) throws android.os.RemoteException;
  public boolean controlDevice(java.lang.String deviceId, java.util.Map params) throws android.os.RemoteException;
}
