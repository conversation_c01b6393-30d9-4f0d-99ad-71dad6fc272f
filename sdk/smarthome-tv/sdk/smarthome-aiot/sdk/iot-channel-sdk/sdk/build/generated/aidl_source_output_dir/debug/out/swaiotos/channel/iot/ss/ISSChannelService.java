/*
 * This file is auto-generated.  DO NOT MODIFY.
 */
package swaiotos.channel.iot.ss;
public interface ISSChannelService extends android.os.IInterface
{
/** Local-side IPC implementation stub class. */
public static abstract class Stub extends android.os.Binder implements swaiotos.channel.iot.ss.ISSChannelService
{
private static final java.lang.String DESCRIPTOR = "swaiotos.channel.iot.ss.ISSChannelService";
/** Construct the stub at attach it to the interface. */
public Stub()
{
this.attachInterface(this, DESCRIPTOR);
}
/**
 * Cast an IBinder object into an swaiotos.channel.iot.ss.ISSChannelService interface,
 * generating a proxy if needed.
 */
public static swaiotos.channel.iot.ss.ISSChannelService asInterface(android.os.IBinder obj)
{
if ((obj==null)) {
return null;
}
android.os.IInterface iin = obj.queryLocalInterface(DESCRIPTOR);
if (((iin!=null)&&(iin instanceof swaiotos.channel.iot.ss.ISSChannelService))) {
return ((swaiotos.channel.iot.ss.ISSChannelService)iin);
}
return new swaiotos.channel.iot.ss.ISSChannelService.Stub.Proxy(obj);
}
@Override public android.os.IBinder asBinder()
{
return this;
}
@Override public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws android.os.RemoteException
{
java.lang.String descriptor = DESCRIPTOR;
switch (code)
{
case INTERFACE_TRANSACTION:
{
reply.writeString(descriptor);
return true;
}
case TRANSACTION_getSessionManager:
{
data.enforceInterface(descriptor);
swaiotos.channel.iot.ss.session.ISessionManagerService _result = this.getSessionManager();
reply.writeNoException();
reply.writeStrongBinder((((_result!=null))?(_result.asBinder()):(null)));
return true;
}
case TRANSACTION_getIMChannel:
{
data.enforceInterface(descriptor);
swaiotos.channel.iot.ss.channel.im.IIMChannelService _result = this.getIMChannel();
reply.writeNoException();
reply.writeStrongBinder((((_result!=null))?(_result.asBinder()):(null)));
return true;
}
case TRANSACTION_getStreamChannel:
{
data.enforceInterface(descriptor);
swaiotos.channel.iot.ss.channel.stream.IStreamChannelService _result = this.getStreamChannel();
reply.writeNoException();
reply.writeStrongBinder((((_result!=null))?(_result.asBinder()):(null)));
return true;
}
case TRANSACTION_getDeviceManager:
{
data.enforceInterface(descriptor);
swaiotos.channel.iot.ss.device.IDeviceManagerService _result = this.getDeviceManager();
reply.writeNoException();
reply.writeStrongBinder((((_result!=null))?(_result.asBinder()):(null)));
return true;
}
case TRANSACTION_getBinder:
{
data.enforceInterface(descriptor);
java.lang.String _arg0;
_arg0 = data.readString();
android.os.IBinder _result = this.getBinder(_arg0);
reply.writeNoException();
reply.writeStrongBinder(_result);
return true;
}
default:
{
return super.onTransact(code, data, reply, flags);
}
}
}
private static class Proxy implements swaiotos.channel.iot.ss.ISSChannelService
{
private android.os.IBinder mRemote;
Proxy(android.os.IBinder remote)
{
mRemote = remote;
}
@Override public android.os.IBinder asBinder()
{
return mRemote;
}
public java.lang.String getInterfaceDescriptor()
{
return DESCRIPTOR;
}
@Override public swaiotos.channel.iot.ss.session.ISessionManagerService getSessionManager() throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
swaiotos.channel.iot.ss.session.ISessionManagerService _result;
try {
_data.writeInterfaceToken(DESCRIPTOR);
mRemote.transact(Stub.TRANSACTION_getSessionManager, _data, _reply, 0);
_reply.readException();
_result = swaiotos.channel.iot.ss.session.ISessionManagerService.Stub.asInterface(_reply.readStrongBinder());
}
finally {
_reply.recycle();
_data.recycle();
}
return _result;
}
@Override public swaiotos.channel.iot.ss.channel.im.IIMChannelService getIMChannel() throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
swaiotos.channel.iot.ss.channel.im.IIMChannelService _result;
try {
_data.writeInterfaceToken(DESCRIPTOR);
mRemote.transact(Stub.TRANSACTION_getIMChannel, _data, _reply, 0);
_reply.readException();
_result = swaiotos.channel.iot.ss.channel.im.IIMChannelService.Stub.asInterface(_reply.readStrongBinder());
}
finally {
_reply.recycle();
_data.recycle();
}
return _result;
}
@Override public swaiotos.channel.iot.ss.channel.stream.IStreamChannelService getStreamChannel() throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
swaiotos.channel.iot.ss.channel.stream.IStreamChannelService _result;
try {
_data.writeInterfaceToken(DESCRIPTOR);
mRemote.transact(Stub.TRANSACTION_getStreamChannel, _data, _reply, 0);
_reply.readException();
_result = swaiotos.channel.iot.ss.channel.stream.IStreamChannelService.Stub.asInterface(_reply.readStrongBinder());
}
finally {
_reply.recycle();
_data.recycle();
}
return _result;
}
@Override public swaiotos.channel.iot.ss.device.IDeviceManagerService getDeviceManager() throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
swaiotos.channel.iot.ss.device.IDeviceManagerService _result;
try {
_data.writeInterfaceToken(DESCRIPTOR);
mRemote.transact(Stub.TRANSACTION_getDeviceManager, _data, _reply, 0);
_reply.readException();
_result = swaiotos.channel.iot.ss.device.IDeviceManagerService.Stub.asInterface(_reply.readStrongBinder());
}
finally {
_reply.recycle();
_data.recycle();
}
return _result;
}
@Override public android.os.IBinder getBinder(java.lang.String name) throws android.os.RemoteException
{
android.os.Parcel _data = android.os.Parcel.obtain();
android.os.Parcel _reply = android.os.Parcel.obtain();
android.os.IBinder _result;
try {
_data.writeInterfaceToken(DESCRIPTOR);
_data.writeString(name);
mRemote.transact(Stub.TRANSACTION_getBinder, _data, _reply, 0);
_reply.readException();
_result = _reply.readStrongBinder();
}
finally {
_reply.recycle();
_data.recycle();
}
return _result;
}
}
static final int TRANSACTION_getSessionManager = (android.os.IBinder.FIRST_CALL_TRANSACTION + 0);
static final int TRANSACTION_getIMChannel = (android.os.IBinder.FIRST_CALL_TRANSACTION + 1);
static final int TRANSACTION_getStreamChannel = (android.os.IBinder.FIRST_CALL_TRANSACTION + 2);
static final int TRANSACTION_getDeviceManager = (android.os.IBinder.FIRST_CALL_TRANSACTION + 3);
static final int TRANSACTION_getBinder = (android.os.IBinder.FIRST_CALL_TRANSACTION + 4);
}
public swaiotos.channel.iot.ss.session.ISessionManagerService getSessionManager() throws android.os.RemoteException;
public swaiotos.channel.iot.ss.channel.im.IIMChannelService getIMChannel() throws android.os.RemoteException;
public swaiotos.channel.iot.ss.channel.stream.IStreamChannelService getStreamChannel() throws android.os.RemoteException;
public swaiotos.channel.iot.ss.device.IDeviceManagerService getDeviceManager() throws android.os.RemoteException;
public android.os.IBinder getBinder(java.lang.String name) throws android.os.RemoteException;
}
