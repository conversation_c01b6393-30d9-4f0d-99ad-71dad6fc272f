# 启用服务功能说明

## 功能概述

在个人中心页面的切换家庭上方新增了一个"启用服务"条目，用户可以通过Toggle开关来控制服务的启用状态。

## 功能特性

- **位置**: 个人中心 -> 切换家庭上方
- **UI组件**: Toggle开关
- **默认状态**: 不启用（false）
- **持久化**: 使用SharedPreferences存储状态

## 实现细节

### 1. 字符串资源
- 中文: `person_center_enable_service` = "启用服务"
- 英文: `person_center_enable_service` = "Enable Service"

### 2. 数据存储
- 存储键: `DataCacheUtil.KEY_ENABLE_SERVICE_STATUS`
- 存储值: `enableServiceStatus`
- 默认值: `false`

### 3. UI组件ID
- 布局ID: `R.id.person_center_enable_service`
- 开关ID: `R.id.person_center_enable_service_switch`

### 4. 核心文件修改

#### ViewsBuilder.java
- 在`getPersonCenter2()`方法中添加了启用服务的布局
- 在`getPersonCentorBaseLayout2()`方法中添加了type=8的处理逻辑
- 为启用服务添加了Toggle开关组件

#### PersonCenterLayout.java
- 添加了启用服务布局的点击和按键监听器
- 实现了`toggleEnableService()`方法来处理开关状态切换
- 集成了数据持久化功能

#### DataCacheUtil.java
- 添加了`KEY_ENABLE_SERVICE_STATUS`常量

#### 资源文件
- `strings.xml`: 添加了中英文字符串资源
- `ids.xml`: 添加了相关的ID定义

## 使用方法

1. 进入个人中心页面
2. 在切换家庭条目上方可以看到"启用服务"条目
3. 点击该条目或使用遥控器右键可以切换Toggle开关状态
4. 开关状态会自动保存，下次进入时会恢复上次的状态

## 技术实现

### 状态管理
```java
// 读取状态
boolean enableServiceStatus = DataCacheUtil.getInstance()
    .getBoolean(DataCacheUtil.KEY_ENABLE_SERVICE_STATUS, false);

// 保存状态
DataCacheUtil.getInstance()
    .putBoolean(DataCacheUtil.KEY_ENABLE_SERVICE_STATUS, newStatus);
```

### 开关切换
```java
private void toggleEnableService() {
    // 获取开关组件
    Switch serviceSwitch = findViewById(R.id.person_center_enable_service_switch);
    // 切换状态
    boolean newStatus = !serviceSwitch.isChecked();
    serviceSwitch.setChecked(newStatus);
    // 保存状态
    DataCacheUtil.getInstance().putBoolean(DataCacheUtil.KEY_ENABLE_SERVICE_STATUS, newStatus);
}
```

## 测试

创建了基本的单元测试文件 `EnableServiceTest.java` 来验证功能的正确性。

## 注意事项

1. 该功能依赖于Android的SharedPreferences进行数据持久化
2. Toggle开关使用了现有的样式资源（`selector_switch_thumb`和`selector_switch_track`）
3. 功能集成在现有的个人中心架构中，遵循了原有的代码风格和设计模式
4. 支持遥控器操作和触摸操作

## 后续扩展

如果需要在其他地方使用启用服务的状态，可以通过以下方式获取：

```java
boolean isServiceEnabled = DataCacheUtil.getInstance()
    .getBoolean(DataCacheUtil.KEY_ENABLE_SERVICE_STATUS, false);
```
