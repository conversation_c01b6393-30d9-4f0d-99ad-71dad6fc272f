package com.swaiot.aiotlib.service.binder.device;

import android.os.RemoteException;

import com.swaiot.aiotlib.common.event.DiscoverNetworkDeviceEvent;
import com.swaiot.aiotlib.common.event.DiscoverWifiDeviceEvent;
import com.swaiot.aiotlib.device.discover.IDiscoverDevice;

import org.greenrobot.eventbus.EventBus;

/**
 * @ClassName: DiscoverDeviceBinder
 * @Author: AwenZeng
 * @CreateDate: 2020/7/6 16:55
 * @Description:
 */
public class DiscoverDeviceBinder extends IDiscoverDevice.Stub {

    @Override
    public void startDiscoverWifiDevice() throws RemoteException {
        EventBus.getDefault().post(new DiscoverWifiDeviceEvent(true));
    }

    @Override
    public void stopDiscoverWifiDevice() throws RemoteException {
        EventBus.getDefault().post(new DiscoverWifiDeviceEvent(false));
    }

    @Override
    public void startDiscoverNetworkDevice() throws RemoteException {
        EventBus.getDefault().post(new DiscoverNetworkDeviceEvent(true));
    }

    @Override
    public void stopDiscoverNetworkDevice() throws RemoteException {
        EventBus.getDefault().post(new DiscoverNetworkDeviceEvent(false));
    }
}
